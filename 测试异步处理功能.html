<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FSA异步处理功能测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 FSA异步处理功能测试 (包含HTTP API模式)</h1>
    
    <div class="test-container">
        <h2>📋 测试说明</h2>
        <p>这个页面用于测试FSA账户测试器的异步后台处理功能。</p>
        <div class="status info">
            <strong>注意：</strong>这只是一个功能测试页面，不会实际访问FSA网站。
        </div>
    </div>

    <div class="test-container">
        <h2>🔧 异步处理核心功能测试</h2>
        
        <div>
            <h3>测试配置</h3>
            <label>批量大小: <input type="number" id="test-batch-size" value="3" min="1" max="10"></label><br><br>
            <label>并发数: <input type="number" id="test-concurrent" value="2" min="1" max="5"></label><br><br>
            <label>处理延迟: <input type="number" id="test-delay" value="1000" min="500" max="5000" step="500"> 毫秒</label><br><br>
            <label>测试数据量: <input type="number" id="test-data-count" value="10" min="5" max="50"></label>
        </div>

        <div style="margin: 20px 0;">
            <button id="start-async-test">开始异步处理测试</button>
            <button id="pause-async-test" disabled>暂停测试</button>
            <button id="stop-async-test" disabled>停止测试</button>
            <button id="clear-log">清空日志</button>
        </div>

        <div>
            <strong>测试状态:</strong> <span id="test-status">等待开始</span><br>
            <strong>进度:</strong> <span id="test-progress">0/0</span><br>
            <strong>活跃任务:</strong> <span id="active-tasks">0</span><br>
            <strong>成功率:</strong> <span id="success-rate">0%</span>
        </div>
    </div>

    <div class="test-container">
        <h2>📊 测试日志</h2>
        <div id="test-log" class="log">等待测试开始...</div>
    </div>

    <script>
        // 模拟FSA异步处理功能
        let isAsyncMode = false;
        let isPaused = false;
        let processingQueue = [];
        let activeWorkers = new Map();
        let processedCount = 0;
        let totalToProcess = 0;
        let workerIdCounter = 0;
        let testResults = [];

        // 配置参数
        let batchSize = 3;
        let maxConcurrent = 2;
        let processingDelay = 1000;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = $('#test-log');
            logElement.append(`[${timestamp}] ${message}<br>`);
            logElement.scrollTop(logElement[0].scrollHeight);
            console.log(message);
        }

        function updateStatus(status) {
            $('#test-status').text(status);
        }

        function updateProgress() {
            $('#test-progress').text(`${processedCount}/${totalToProcess}`);
            $('#active-tasks').text(activeWorkers.size);
            
            const successCount = testResults.filter(r => r.status === 'SUCCESS').length;
            const successRate = testResults.length > 0 ? 
                (successCount / testResults.length * 100).toFixed(1) + '%' : '0%';
            $('#success-rate').text(successRate);
        }

        // 生成测试数据
        function generateTestData(count) {
            const testData = [];
            for (let i = 1; i <= count; i++) {
                testData.push({
                    id: i,
                    firstName: `Test${i}`,
                    lastName: `User${i}`,
                    data: `测试数据 ${i}`
                });
            }
            return testData;
        }

        // 模拟异步处理单个项目
        async function processItemAsync(testItem, taskId) {
            log(`${taskId}: 开始处理 ${testItem.firstName} ${testItem.lastName}`);
            
            // 随机延迟模拟真实处理
            const randomDelay = Math.random() * 1000 + 500;
            await new Promise(resolve => setTimeout(resolve, randomDelay));
            
            // 模拟随机成功/失败
            const success = Math.random() > 0.2; // 80%成功率
            
            if (success) {
                testResults.push({id: testItem.id, status: 'SUCCESS', message: '处理成功'});
                log(`${taskId}: 处理成功`);
                return 'SUCCESS';
            } else {
                testResults.push({id: testItem.id, status: 'ERROR', message: '模拟错误'});
                log(`${taskId}: 处理失败 - 模拟错误`);
                throw new Error('模拟处理错误');
            }
        }

        // 创建异步工作器
        async function createAsyncWorker(workerId) {
            const id = `worker-${workerId}`;
            log(`启动异步工作器: ${id}`);
            
            while (processingQueue.length > 0 && !isPaused) {
                const batch = processingQueue.splice(0, batchSize);
                if (batch.length === 0) break;
                
                log(`${id} 开始处理批次，数量: ${batch.length}`);
                
                const batchResults = await Promise.allSettled(
                    batch.map(async (testItem) => {
                        const taskId = `${id}-task-${++workerIdCounter}`;
                        activeWorkers.set(taskId, {
                            data: testItem,
                            status: 'processing',
                            startTime: Date.now()
                        });
                        
                        try {
                            const result = await processItemAsync(testItem, taskId);
                            processedCount++;
                            updateProgress();
                            return result;
                        } catch (error) {
                            log(`${taskId} 处理失败: ${error.message}`);
                            return null;
                        } finally {
                            activeWorkers.delete(taskId);
                            updateProgress();
                        }
                    })
                );
                
                const successful = batchResults.filter(r => r.status === 'fulfilled').length;
                const failed = batchResults.filter(r => r.status === 'rejected').length;
                log(`${id} 批次完成: 成功 ${successful}, 失败 ${failed}`);
                
                if (processingQueue.length > 0) {
                    const successRate = successful / batchResults.length;
                    const adaptiveDelay = successRate > 0.8 ? processingDelay : processingDelay * 1.5;
                    await new Promise(resolve => setTimeout(resolve, adaptiveDelay));
                }
            }
            
            log(`异步工作器 ${id} 完成任务`);
            return id;
        }

        // 开始异步处理测试
        async function startAsyncTest() {
            if (isAsyncMode) return;
            
            // 获取配置
            batchSize = parseInt($('#test-batch-size').val()) || 3;
            maxConcurrent = parseInt($('#test-concurrent').val()) || 2;
            processingDelay = parseInt($('#test-delay').val()) || 1000;
            const dataCount = parseInt($('#test-data-count').val()) || 10;
            
            // 生成测试数据
            const testData = generateTestData(dataCount);
            processingQueue = [...testData];
            totalToProcess = processingQueue.length;
            processedCount = 0;
            workerIdCounter = 0;
            testResults = [];
            
            isAsyncMode = true;
            isPaused = false;
            
            log(`开始异步处理测试，总数据量: ${totalToProcess}`);
            log(`配置: 批量大小=${batchSize}, 并发数=${maxConcurrent}, 延迟=${processingDelay}ms`);
            updateStatus('异步处理中...');
            
            $('#start-async-test').prop('disabled', true);
            $('#pause-async-test').prop('disabled', false);
            $('#stop-async-test').prop('disabled', false);
            
            try {
                const workers = [];
                for (let i = 0; i < maxConcurrent; i++) {
                    workers.push(createAsyncWorker(i));
                }
                
                const results = await Promise.allSettled(workers);
                const successful = results.filter(r => r.status === 'fulfilled').length;
                const failed = results.filter(r => r.status === 'rejected').length;
                
                log(`异步处理完成: 成功 ${successful} 个工作器, 失败 ${failed} 个工作器`);
                
            } catch (error) {
                log(`异步处理出现意外错误: ${error.message}`);
            } finally {
                isAsyncMode = false;
                updateStatus('测试完成');
                $('#start-async-test').prop('disabled', false);
                $('#pause-async-test').prop('disabled', true);
                $('#stop-async-test').prop('disabled', true);
                
                // 显示最终统计
                const successCount = testResults.filter(r => r.status === 'SUCCESS').length;
                const errorCount = testResults.filter(r => r.status === 'ERROR').length;
                log(`最终统计: 总处理 ${processedCount} 条，成功 ${successCount} 条，失败 ${errorCount} 条`);
            }
        }

        // 事件绑定
        $('#start-async-test').click(startAsyncTest);
        
        $('#pause-async-test').click(function() {
            isPaused = !isPaused;
            const status = isPaused ? '已暂停' : '异步处理中...';
            updateStatus(status);
            $(this).text(isPaused ? '恢复测试' : '暂停测试');
            log(`测试状态: ${status}`);
        });
        
        $('#stop-async-test').click(function() {
            isAsyncMode = false;
            isPaused = true;
            activeWorkers.clear();
            updateStatus('已停止');
            $('#start-async-test').prop('disabled', false);
            $('#pause-async-test').prop('disabled', true);
            $('#stop-async-test').prop('disabled', true);
            log('测试已停止');
        });
        
        $('#clear-log').click(function() {
            $('#test-log').html('日志已清空...<br>');
        });

        // 初始化
        updateProgress();
        log('异步处理功能测试页面已加载');
    </script>
</body>
</html>
