// ==UserScript==
// @name         FSA账户测试器 (多线程版)
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  自动测试FSA账户注册状态 - 支持多线程并发处理，大幅提升测试速度
// <AUTHOR>
// @match        https://studentaid.gov/fsa-id/create-account/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_log
// @grant        GM_openInTab
// @require      https://code.jquery.com/jquery-3.6.0.min.js
// ==/UserScript==

(function() {
    'use strict';

    // 测试数据 - 现在支持导入自定义数据
    let testData = [];

    // 测试结果存储
    let testResults = [];
    let currentIndex = 0;
    let isTestingInProgress = false;
    let retryCount = 0;
    const maxRetries = 3;

    // 异步后台处理配置
    let batchSize = 3; // 批量处理大小
    let processingDelay = 2000; // 处理间隔(毫秒)
    let maxConcurrent = 2; // 最大并发数
    let isAsyncMode = false; // 异步模式标志
    let processingQueue = []; // 处理队列
    let activeWorkers = new Map(); // 活跃工作器 {id: {data, promise, status}}
    let asyncTimer = null; // 异步定时器
    let isPaused = false; // 暂停标志
    let processedCount = 0; // 已处理计数
    let totalToProcess = 0; // 总处理数量
    let workerIdCounter = 0; // 工作器ID计数器

    // 异步处理状态
    const ASYNC_STATUS = {
        WAITING: 'waiting',
        PROCESSING: 'processing',
        COMPLETED: 'completed',
        ERROR: 'error',
        PAUSED: 'paused'
    };

    // 异步后台处理核心函数
    async function startAsyncProcessing() {
        if (isAsyncMode || testData.length === 0) return;

        isAsyncMode = true;
        processingQueue = [...testData]; // 复制数据到处理队列
        totalToProcess = processingQueue.length;
        processedCount = 0;
        workerIdCounter = 0;

        GM_log('开始异步后台处理，总数据量:', totalToProcess);
        updateAsyncStatus('异步处理中...');

        // 使用Promise.allSettled确保所有任务都完成（无论成功失败）
        const workers = [];
        for (let i = 0; i < maxConcurrent; i++) {
            workers.push(createAsyncWorker(i));
        }

        try {
            // 等待所有工作器完成，使用allSettled确保不会因单个错误而停止
            const results = await Promise.allSettled(workers);

            // 统计结果
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;

            GM_log(`异步处理完成: 成功 ${successful} 个工作器, 失败 ${failed} 个工作器`);

        } catch (error) {
            GM_log('异步处理出现意外错误:', error);
        } finally {
            isAsyncMode = false;
            updateAsyncStatus('异步处理完成');
            completeTesting();
        }
    }

    // 创建异步工作器
    async function createAsyncWorker(workerId) {
        const id = `worker-${workerId}`;
        GM_log(`启动异步工作器: ${id}`);

        while (processingQueue.length > 0 && !isPaused) {
            // 从队列中取出一批数据进行处理
            const batch = processingQueue.splice(0, batchSize);
            if (batch.length === 0) break;

            GM_log(`${id} 开始处理批次，数量: ${batch.length}`);

            // 使用Promise.allSettled处理批次，确保单个失败不影响其他
            const batchResults = await Promise.allSettled(
                batch.map(async (testItem, index) => {
                    const taskId = `${id}-task-${++workerIdCounter}`;
                    activeWorkers.set(taskId, {
                        data: testItem,
                        status: ASYNC_STATUS.PROCESSING,
                        startTime: Date.now()
                    });

                    try {
                        const result = await processItemAsync(testItem, taskId);
                        processedCount++;
                        updateProgressDisplay();
                        return result;
                    } catch (error) {
                        GM_log(`${taskId} 处理失败:`, error);
                        recordResult(testItem, "ERROR", `异步处理错误: ${error.message}`);
                        return null;
                    } finally {
                        activeWorkers.delete(taskId);
                    }
                })
            );

            // 统计批次结果
            const successful = batchResults.filter(r => r.status === 'fulfilled').length;
            const failed = batchResults.filter(r => r.status === 'rejected').length;
            GM_log(`${id} 批次完成: 成功 ${successful}, 失败 ${failed}`);

            // 批次间智能延迟，根据成功率调整
            if (processingQueue.length > 0) {
                const successRate = successful / batchResults.length;
                const adaptiveDelay = successRate > 0.8 ? processingDelay : processingDelay * 1.5;
                await new Promise(resolve => setTimeout(resolve, adaptiveDelay));
            }
        }

        GM_log(`异步工作器 ${id} 完成任务`);
        return id;
    }

    // 异步处理单个测试项目
    async function processItemAsync(testItem, taskId) {
        GM_log(`${taskId}: 开始异步处理 ${testItem.firstName} ${testItem.lastName}`);

        // 使用随机延迟避免同时请求，模拟真实的异步处理
        const randomDelay = Math.random() * 1000 + 500; // 500-1500ms随机延迟
        await new Promise(resolve => setTimeout(resolve, randomDelay));

        try {
            // 检查页面状态
            if (!window.location.href.includes('personal-info')) {
                await navigateToPersonalInfoAsync();
            }

            // 异步填写表单
            const formFilled = await fillFormAsync(testItem);
            if (!formFilled) {
                throw new Error('表单填写失败');
            }

            // 异步提交并检查结果
            const result = await submitAndCheckAsync(testItem);
            GM_log(`${taskId}: 处理完成，结果: ${result}`);
            return result;

        } catch (error) {
            GM_log(`${taskId}: 处理失败:`, error);
            throw error;
        }
    }

    // 保留原有的processSingleItem函数以兼容顺序模式
    async function processSingleItem(testItem, processId) {
        GM_log(`${processId}: 开始处理 ${testItem.firstName} ${testItem.lastName}`);

        // 模拟填写表单和提交的过程
        return new Promise((resolve, reject) => {
            // 使用setTimeout模拟异步处理，避免阻塞UI
            setTimeout(async () => {
                try {
                    // 检查页面状态
                    if (!window.location.href.includes('personal-info')) {
                        // 如果不在正确页面，先导航
                        await navigateToPersonalInfoAsync();
                    }

                    // 填写表单
                    const success = await fillFormAsync(testItem);
                    if (success) {
                        // 提交并检查结果
                        const result = await submitAndCheckAsync(testItem);
                        resolve(result);
                    } else {
                        reject(new Error('表单填写失败'));
                    }
                } catch (error) {
                    reject(error);
                }
            }, Math.random() * 500); // 随机延迟，避免同时请求
        });
    }

    // 异步导航到个人信息页面
    async function navigateToPersonalInfoAsync() {
        return new Promise((resolve, reject) => {
            if (window.location.href.includes('personal-info')) {
                resolve(true);
                return;
            }

            if (window.location.href.includes('launch')) {
                const getStartedButton = findButtonByText("Get Started");
                if (getStartedButton) {
                    getStartedButton.click();
                    setTimeout(() => {
                        if (window.location.href.includes('personal-info')) {
                            resolve(true);
                        } else {
                            reject(new Error('导航失败'));
                        }
                    }, 3000);
                } else {
                    reject(new Error('未找到Get Started按钮'));
                }
            } else {
                window.location.href = "https://studentaid.gov/fsa-id/create-account/personal-info";
                setTimeout(() => resolve(true), 3000);
            }
        });
    }

    // 异步填写表单
    async function fillFormAsync(testItem) {
        return new Promise((resolve) => {
            try {
                // 获取表单元素
                const firstNameField = document.querySelector('#fsa_Input_FirstName');
                const lastNameField = document.querySelector('#fsa_Input_LastName');
                const monthField = document.querySelector('#fsa_Input_DateOfBirthMonth');
                const dayField = document.querySelector('#fsa_Input_DateOfBirthDay');
                const yearField = document.querySelector('#fsa_Input_DateOfBirthYear');
                const ssnField = document.querySelector('#fsa_Input_Ssn');

                if (!firstNameField || !lastNameField || !monthField || !dayField || !yearField || !ssnField) {
                    resolve(false);
                    return;
                }

                // 快速填写表单（减少延迟）
                firstNameField.value = testItem.firstName;
                triggerInputEvent(firstNameField);

                lastNameField.value = testItem.lastName;
                triggerInputEvent(lastNameField);

                monthField.value = testItem.birthDate.month;
                triggerInputEvent(monthField);

                dayField.value = testItem.birthDate.day;
                triggerInputEvent(dayField);

                yearField.value = testItem.birthDate.year;
                triggerInputEvent(yearField);

                ssnField.value = testItem.ssn;
                triggerInputEvent(ssnField);

                // 短暂延迟确保表单更新
                setTimeout(() => resolve(true), 500);

            } catch (error) {
                GM_log('填写表单异步错误:', error);
                resolve(false);
            }
        });
    }

    // 异步提交并检查结果
    async function submitAndCheckAsync(testItem) {
        return new Promise((resolve, reject) => {
            try {
                const continueButton = document.querySelector('#fsa_Button_PersonalInformationContinue');
                if (!continueButton) {
                    reject(new Error('未找到Continue按钮'));
                    return;
                }

                continueButton.click();

                // 等待响应并检查结果
                setTimeout(() => {
                    const pageUrl = window.location.href;
                    const pageContent = document.body.innerText;

                    if (pageContent.includes("Account already exists")) {
                        recordResult(testItem, "EXISTS", "账户已存在");
                        resolve("EXISTS");
                    } else if (pageUrl.includes('account-info')) {
                        recordResult(testItem, "SUCCESS", "可以创建账户");

                        // 返回上一步
                        const previousButton = document.querySelector('#fsa_Button_ShowPreviousStep');
                        if (previousButton) {
                            previousButton.click();
                        }
                        resolve("SUCCESS");
                    } else {
                        recordResult(testItem, "UNKNOWN", "未知状态");
                        resolve("UNKNOWN");
                    }
                }, 3000);

            } catch (error) {
                reject(error);
            }
        });
    }

    // 更新异步处理状态显示
    function updateAsyncStatus(status) {
        $('#test-status').text(status);
        $('#current-progress').text(`${processedCount}/${totalToProcess}`);

        // 显示活跃工作器数量
        if (activeWorkers.size > 0) {
            $('#test-status').text(`${status} (${activeWorkers.size} 个活跃任务)`);
        }
    }

    // 暂停/恢复异步处理
    function toggleAsyncProcessing() {
        isPaused = !isPaused;
        const status = isPaused ? '已暂停' : '异步处理中...';
        updateAsyncStatus(status);
        GM_log('异步处理状态:', status);

        // 更新按钮文本
        $('#pause-test').text(isPaused ? '恢复处理' : '暂停处理');
    }

    // 获取异步处理统计信息
    function getAsyncStats() {
        const stats = {
            total: totalToProcess,
            processed: processedCount,
            remaining: totalToProcess - processedCount,
            activeWorkers: activeWorkers.size,
            queueLength: processingQueue.length,
            successRate: testResults.length > 0 ?
                (testResults.filter(r => r.status === 'SUCCESS').length / testResults.length * 100).toFixed(1) + '%' : '0%'
        };
        return stats;
    }

    // 解析导入的数据 - 支持多种直接粘贴格式
    function parseImportedData(text) {
        const lines = text.trim().split('\n');
        const parsedData = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue; // 跳过空行

            // 尝试多种分隔符：TAB、逗号、多个空格、单个空格
            let parts = [];

            // 优先尝试TAB分隔（保持向后兼容）
            if (line.includes('\t')) {
                parts = line.split('\t');
            }
            // 尝试逗号分隔
            else if (line.includes(',')) {
                parts = line.split(',');
            }
            // 尝试多个空格分隔
            else if (line.includes('  ')) {
                parts = line.split(/\s{2,}/); // 匹配2个或更多空格
            }
            // 最后尝试单个空格分隔
            else {
                parts = line.split(/\s+/); // 匹配一个或多个空格
            }

            // 清理数据，移除多余的空白和引号
            parts = parts.map(part => part.trim().replace(/^["']|["']$/g, ''));

            if (parts.length >= 4) {
                const firstName = parts[0].trim();
                let lastName = parts[1].trim();
                const birthDateStr = parts[2].trim(); // 支持多种日期格式
                const ssn = parts[3].trim();

                // 处理姓氏中的空格 - 只保留最后一个部分
                if (lastName.includes(' ')) {
                    const lastNameParts = lastName.split(/\s+/);
                    const originalLastName = lastName;
                    lastName = lastNameParts[lastNameParts.length - 1]; // 只保留最后一个部分
                    console.log(`姓氏处理: "${originalLastName}" → "${lastName}"`);
                }

                // 解析出生日期 - 支持多种格式
                const birthDate = parseBirthDate(birthDateStr);

                if (birthDate) {
                    parsedData.push({
                        firstName: firstName,
                        lastName: lastName,
                        birthDate: birthDate,
                        ssn: ssn
                    });
                } else {
                    console.warn(`第 ${i + 1} 行日期格式错误: ${birthDateStr}`);
                }
            } else {
                console.warn(`第 ${i + 1} 行数据格式错误 (需要至少4个字段): ${line}`);
            }
        }

        return parsedData;
    }

    // 解析出生日期 - 支持多种格式
    function parseBirthDate(dateStr) {
        if (!dateStr) return null;
        
        // 移除所有非数字和分隔符的字符
        const cleanDate = dateStr.replace(/[^\d\/\-\.]/g, '');
        
        let dateParts = [];
        
        // 尝试不同的分隔符
        if (cleanDate.includes('/')) {
            dateParts = cleanDate.split('/');
        } else if (cleanDate.includes('-')) {
            dateParts = cleanDate.split('-');
        } else if (cleanDate.includes('.')) {
            dateParts = cleanDate.split('.');
        } else if (cleanDate.length === 8) {
            // 尝试解析MMDDYYYY或YYYYMMDD格式
            if (cleanDate.substring(0, 4) > '1900') {
                // YYYYMMDD格式
                let year = cleanDate.substring(0, 4);
                let month = cleanDate.substring(4, 6);
                let day = cleanDate.substring(6, 8);
                
                // 处理日期为00的情况，自动设为01
                if (day === '00') {
                    day = '01';
                    console.log(`日期处理: ${dateStr} 中的日期00已自动调整为01`);
                }
                
                dateParts = [month, day, year];
            } else {
                // MMDDYYYY格式
                dateParts = [cleanDate.substring(0, 2), cleanDate.substring(2, 4), cleanDate.substring(4, 8)];
            }
        }
        
        if (dateParts.length === 3) {
            let month, day, year;
            
            // 判断日期格式 (MM/DD/YYYY 或 DD/MM/YYYY 或 YYYY/MM/DD)
            if (dateParts[0].length === 4) {
                // YYYY/MM/DD格式
                year = dateParts[0];
                month = dateParts[1];
                day = dateParts[2];
            } else if (parseInt(dateParts[0]) > 12) {
                // 第一个数字大于12，可能是DD/MM/YYYY格式
                day = dateParts[0];
                month = dateParts[1];
                year = dateParts[2];
            } else {
                // 默认MM/DD/YYYY格式
                month = dateParts[0];
                day = dateParts[1];
                year = dateParts[2];
            }
            
            // 再次检查日期为00的情况
            if (day === '00') {
                day = '01';
                console.log(`日期处理: 日期字段00已自动调整为01`);
            }
            
            return {
                month: month.padStart(2, '0'),
                day: day.padStart(2, '0'),
                year: year
            };
        }
        
        return null;
    }

    // 创建控制面板
    function createControlPanel() {
        const panel = $(`
            <div id="fsa-tester-panel" style="
                position: fixed;
                top: 10px;
                right: 10px;
                width: 380px;
                background: white;
                border: 2px solid #0066cc;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 14px;
            ">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0; color: #0066cc;">FSA账户测试器</h3>
                    <button id="minimize-panel" style="background: none; border: none; font-size: 18px; cursor: pointer;">−</button>
                </div>

                <div id="panel-content">
                    <!-- 数据导入区域 -->
                    <div style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <div style="margin-bottom: 10px;"><strong>数据导入:</strong></div>
                        <textarea id="data-input" placeholder="请直接粘贴资料，支持多种格式：
SAMANTHA STONE 07/21/1998 231811389
JOHN DOE 12/15/1995 123456789
或逗号分隔、TAB分隔等..." style="
                            width: 100%;
                            height: 80px;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 3px;
                            margin-bottom: 8px;
                            font-family: monospace;
                            font-size: 12px;
                            resize: vertical;
                        "></textarea>
                        <div style="font-size: 12px; color: #666; margin-bottom: 8px;">
                            <strong>支持多种格式直接粘贴:</strong><br>
                            • 空格分隔: SAMANTHA STONE 07/21/1998 231811389<br>
                            • 逗号分隔: SAMANTHA,STONE,07/21/1998,231811389<br>
                            • TAB分隔: SAMANTHA[TAB]STONE[TAB]07/21/1998[TAB]231811389<br>
                            <strong>日期格式:</strong> MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD, MM-DD-YYYY, YYYYMM00 等<br>
                            <strong>姓氏处理:</strong> 有空格的姓氏自动保留最后部分 (TRAN NGUYEN → NGUYEN)
                        </div>
                        <button id="import-data" style="
                            background: #17a2b8;
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 3px;
                            cursor: pointer;
                            font-size: 12px;
                        ">导入数据</button>
                        <button id="clear-data" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 6px 12px;
                            border-radius: 3px;
                            cursor: pointer;
                            font-size: 12px;
                            margin-left: 5px;
                        ">清空数据</button>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <div><strong>总数据量:</strong> <span id="data-count">${testData.length}</span> 条</div>
                        <div><strong>当前进度:</strong> <span id="current-progress">0/${testData.length}</span></div>
                        <div><strong>测试状态:</strong> <span id="test-status">${testData.length > 0 ? '待开始' : '待导入数据'}</span></div>
                        <div><strong>重试次数:</strong> <span id="retry-count">0/${maxRetries}</span></div>
                    </div>

                    <!-- 处理模式选择 -->
                    <div style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <div style="margin-bottom: 10px;"><strong>处理模式:</strong></div>
                        <div style="margin-bottom: 10px;">
                            <label style="margin-right: 15px;">
                                <input type="radio" name="processing-mode" value="sequential" checked>
                                顺序处理 (原模式)
                            </label>
                            <label>
                                <input type="radio" name="processing-mode" value="background">
                                后台批量处理 (快速)
                            </label>
                        </div>
                        <div id="background-settings" style="display: none; font-size: 12px; color: #666;">
                            <div style="margin-bottom: 5px;">
                                <label>批量大小: <input type="number" id="batch-size-input" value="5" min="1" max="10" style="width: 50px;"></label>
                                <label style="margin-left: 15px;">并发数: <input type="number" id="concurrent-input" value="3" min="1" max="5" style="width: 50px;"></label>
                            </div>
                            <div style="margin-bottom: 5px;">
                                <label>处理间隔: <input type="number" id="delay-input" value="1000" min="500" max="5000" step="500" style="width: 60px;"> 毫秒</label>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <button id="start-test" style="
                            background: #28a745;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-right: 10px;
                        " disabled>开始测试</button>

                        <button id="stop-test" style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-right: 10px;
                        " disabled>停止测试</button>

                        <button id="pause-test" style="
                            background: #ffc107;
                            color: black;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-right: 10px;
                            display: none;
                        ">暂停处理</button>

                        <button id="export-results" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            cursor: pointer;
                        ">导出结果</button>
                    </div>

                    <div id="current-test-info" style="
                        background: #f8f9fa;
                        padding: 10px;
                        border-radius: 4px;
                        margin-bottom: 15px;
                        display: none;
                    ">
                        <div><strong>当前测试:</strong></div>
                        <div id="current-name"></div>
                        <div id="current-ssn"></div>
                    </div>

                    <div id="results-summary" style="
                        background: #e9ecef;
                        padding: 10px;
                        border-radius: 4px;
                        max-height: 200px;
                        overflow-y: auto;
                    ">
                        <div><strong>测试结果:</strong></div>
                        <div id="results-list">暂无结果</div>
                    </div>
                </div>
            </div>
        `);

        $('body').append(panel);

        // 最小化功能
        $('#minimize-panel').click(function() {
            const content = $('#panel-content');
            const button = $(this);
            if (content.is(':visible')) {
                content.hide();
                button.text('+');
            } else {
                content.show();
                button.text('−');
            }
        });

        // 绑定事件
        $('#start-test').click(startTesting);
        $('#stop-test').click(stopTesting);
        $('#pause-test').click(toggleAsyncProcessing);
        $('#export-results').click(exportResults);
        $('#import-data').click(importData);
        $('#clear-data').click(clearData);

        // 处理模式切换事件
        $('input[name="processing-mode"]').change(function() {
            const mode = $(this).val();
            if (mode === 'background') {
                $('#background-settings').show();
                $('#pause-test').show();
            } else {
                $('#background-settings').hide();
                $('#pause-test').hide();
            }
        });

        // 后台处理参数更新事件
        $('#batch-size-input').change(function() {
            batchSize = parseInt($(this).val()) || 5;
            GM_log('批量大小更新为:', batchSize);
        });

        $('#concurrent-input').change(function() {
            maxConcurrent = parseInt($(this).val()) || 3;
            GM_log('并发数更新为:', maxConcurrent);
        });

        $('#delay-input').change(function() {
            processingDelay = parseInt($(this).val()) || 1000;
            GM_log('处理间隔更新为:', processingDelay);
        });
    }

    // 导入数据
    function importData() {
        const dataInput = document.getElementById('data-input');
        const text = dataInput.value.trim();

        if (!text) {
            alert('请先粘贴数据到文本框中');
            return;
        }

        try {
            const newData = parseImportedData(text);

            if (newData.length === 0) {
                alert('未能解析到有效数据，请检查数据格式\n\n支持的格式：\n• 空格分隔: SAMANTHA STONE 07/21/1998 231811389\n• 逗号分隔: SAMANTHA,STONE,07/21/1998,231811389\n• TAB分隔: SAMANTHA[TAB]STONE[TAB]07/21/1998[TAB]231811389\n\n每行一条记录，包含：姓名 姓氏 出生日期 SSN');
                return;
            }

            testData = newData;
            updateDataCount();
            updateProgressDisplay();

            // 启用开始测试按钮
            $('#start-test').prop('disabled', false);
            $('#test-status').text('待开始');

            alert(`成功导入 ${newData.length} 条数据`);
            GM_log('导入数据:', testData);

            // 保存导入的数据
            GM_setValue('fsa_imported_data', JSON.stringify(testData));

        } catch (error) {
            alert('数据解析错误: ' + error.message);
            GM_log('导入数据错误:', error);
        }
    }

    // 清空数据
    function clearData() {
        if (isTestingInProgress) {
            alert('测试进行中，无法清空数据');
            return;
        }

        if (testData.length === 0) {
            alert('当前没有数据');
            return;
        }

        if (confirm('确定要清空所有导入的数据吗？')) {
            testData = [];
            testResults = [];
            currentIndex = 0;

            updateDataCount();
            updateProgressDisplay();
            updateResultsDisplay();

            // 禁用开始测试按钮
            $('#start-test').prop('disabled', true);
            $('#test-status').text('待导入数据');

            // 清空文本输入
            document.getElementById('data-input').value = '';

            // 清除保存的数据
            GM_setValue('fsa_imported_data', null);
            GM_setValue('fsa_test_results', null);

            alert('数据已清空');
        }
    }

    // 更新数据计数显示
    function updateDataCount() {
        $('#data-count').text(testData.length);
    }

    // 更新进度显示
    function updateProgressDisplay() {
        if (isAsyncMode) {
            $('#current-progress').text(`${processedCount}/${totalToProcess}`);

            // 显示活跃工作器数
            if (activeWorkers.size > 0) {
                $('#test-status').text(`异步处理中... (${activeWorkers.size} 个活跃任务)`);
            }

            // 显示队列剩余数量
            if (processingQueue.length > 0) {
                $('#test-status').text(`异步处理中... (${activeWorkers.size} 个活跃任务, ${processingQueue.length} 个等待)`);
            }
        } else {
            $('#current-progress').text(`${currentIndex}/${testData.length}`);
        }
    }

    // 开始测试
    function startTesting() {
        if (isTestingInProgress || isBackgroundMode) return;

        // 检查是否有测试数据
        if (testData.length === 0) {
            alert('请先导入测试数据');
            return;
        }

        // 检查处理模式
        const processingMode = $('input[name="processing-mode"]:checked').val();

        isTestingInProgress = true;
        currentIndex = 0;
        testResults = [];

        $('#start-test').prop('disabled', true);
        $('#stop-test').prop('disabled', false);

        GM_log('开始FSA账户测试...');
        GM_log('测试数据量:', testData.length);
        GM_log('处理模式:', processingMode);

        if (processingMode === 'background') {
            // 异步后台处理模式
            $('#test-status').text('异步处理中...');
            $('#pause-test').prop('disabled', false);
            startAsyncProcessing();
        } else {
            // 传统顺序处理模式
            $('#test-status').text('顺序测试中...');
            $('#current-test-info').show();

            // 检查当前页面
            if (!window.location.href.includes('personal-info')) {
                navigateToPersonalInfo();
            } else {
                processNextTest();
            }
        }
    }

    // 导航到个人信息页面
    function navigateToPersonalInfo() {
        GM_log('导航到个人信息页面...');

        // 如果在启动页面，点击Get Started
        if (window.location.href.includes('launch')) {
            const getStartedButton = findButtonByText("Get Started");

            if (getStartedButton) {
                GM_log('点击Get Started按钮...');
                getStartedButton.click();

                // 等待页面加载
                setTimeout(() => {
                    if (window.location.href.includes('personal-info')) {
                        GM_log('已进入个人信息页面');
                        processNextTest();
                    } else {
                        GM_log('导航失败，重试...');
                        retryCount++;
                        if (retryCount < maxRetries) {
                            setTimeout(navigateToPersonalInfo, 2000);
                        } else {
                            recordResult({firstName: "导航", lastName: "错误"}, "ERROR", "无法导航到个人信息页面");
                            stopTesting();
                        }
                    }
                }, 3000);
            } else {
                GM_log('未找到Get Started按钮');
                recordResult({firstName: "导航", lastName: "错误"}, "ERROR", "未找到Get Started按钮");
                stopTesting();
            }
        } else {
            // 直接导航到个人信息页面
            window.location.href = "https://studentaid.gov/fsa-id/create-account/personal-info";

            // 等待页面加载
            setTimeout(() => {
                processNextTest();
            }, 3000);
        }
    }

    // 停止测试
    function stopTesting() {
        isTestingInProgress = false;

        // 停止异步处理
        if (isAsyncMode) {
            isAsyncMode = false;
            isPaused = true;
            if (asyncTimer) {
                clearTimeout(asyncTimer);
                asyncTimer = null;
            }
            // 清理活跃工作器
            activeWorkers.clear();
            $('#pause-test').prop('disabled', true);
        }

        $('#start-test').prop('disabled', false);
        $('#stop-test').prop('disabled', true);
        $('#test-status').text('已停止');
        $('#current-test-info').hide();

        GM_log('测试已停止');

        // 显示停止时的统计信息
        if (processedCount > 0) {
            const successCount = testResults.filter(r => r.status === 'SUCCESS').length;
            const existsCount = testResults.filter(r => r.status === 'EXISTS').length;
            const errorCount = testResults.filter(r => r.status === 'ERROR' || r.status === 'UNKNOWN').length;

            GM_log(`停止时统计: 已处理 ${processedCount} 条，成功 ${successCount} 条，已存在 ${existsCount} 条，错误 ${errorCount} 条`);
        }
    }

    // 处理下一个测试
    function processNextTest() {
        if (!isTestingInProgress || currentIndex >= testData.length) {
            completeTesting();
            return;
        }

        const testItem = testData[currentIndex];
        updateCurrentTestInfo(testItem);

        GM_log(`测试第 ${currentIndex + 1}/${testData.length} 条数据: ${testItem.firstName} ${testItem.lastName}`);

        // 重置重试计数
        retryCount = 0;

        // 填写表单
        fillFormWithRetry(testItem);
    }

    // 更新当前测试信息
    function updateCurrentTestInfo(testItem) {
        updateProgressDisplay();
        $('#current-name').text(`姓名: ${testItem.firstName} ${testItem.lastName}`);
        $('#current-ssn').text(`SSN: ${testItem.ssn} (出生: ${testItem.birthDate.month}/${testItem.birthDate.day}/${testItem.birthDate.year})`);
        $('#retry-count').text(`${retryCount}/${maxRetries}`);
    }

    // 带重试的表单填写
    function fillFormWithRetry(testItem) {
        // 检查必要元素是否存在
        // 使用MCP环境中观察到的实际ID和选择器
        const firstNameField = document.querySelector('#fsa_Input_FirstName');
        const lastNameField = document.querySelector('#fsa_Input_LastName');
        const monthField = document.querySelector('#fsa_Input_DateOfBirthMonth');
        const dayField = document.querySelector('#fsa_Input_DateOfBirthDay');
        const yearField = document.querySelector('#fsa_Input_DateOfBirthYear');
        const ssnField = document.querySelector('#fsa_Input_Ssn');

        if (!firstNameField || !lastNameField || !monthField || !dayField || !yearField || !ssnField) {
            if (retryCount < maxRetries) {
                retryCount++;
                GM_log(`表单元素未找到，重试 ${retryCount}/${maxRetries}`);
                $('#retry-count').text(`${retryCount}/${maxRetries}`);
                setTimeout(() => fillFormWithRetry(testItem), 2000);
                return;
            } else {
                GM_log('表单元素未找到，跳过此项');
                recordResult(testItem, "ERROR", '表单元素未找到');
                currentIndex++;
                processNextTest();
                return;
            }
        }

        fillForm(testItem);
    }

    // 填写表单
    function fillForm(testItem) {
        try {
            GM_log('填写表单...');

            // 获取表单元素 - 使用ID选择器
            const firstNameField = document.querySelector('#fsa_Input_FirstName');
            const lastNameField = document.querySelector('#fsa_Input_LastName');
            const monthField = document.querySelector('#fsa_Input_DateOfBirthMonth');
            const dayField = document.querySelector('#fsa_Input_DateOfBirthDay');
            const yearField = document.querySelector('#fsa_Input_DateOfBirthYear');
            const ssnField = document.querySelector('#fsa_Input_Ssn');

            // 清空现有内容
            firstNameField.value = '';
            lastNameField.value = '';
            monthField.value = '';
            dayField.value = '';
            yearField.value = '';
            ssnField.value = '';

            // 触发输入事件
            triggerInputEvent(firstNameField);
            triggerInputEvent(lastNameField);
            triggerInputEvent(monthField);
            triggerInputEvent(dayField);
            triggerInputEvent(yearField);
            triggerInputEvent(ssnField);

            // 填写姓名
            setTimeout(() => {
                firstNameField.value = testItem.firstName;
                triggerInputEvent(firstNameField);
                GM_log(`填写姓名: ${testItem.firstName}`);
            }, 200);

            setTimeout(() => {
                lastNameField.value = testItem.lastName;
                triggerInputEvent(lastNameField);
                GM_log(`填写姓氏: ${testItem.lastName}`);
            }, 400);

            // 填写出生日期
            setTimeout(() => {
                monthField.value = testItem.birthDate.month;
                triggerInputEvent(monthField);
                GM_log(`填写月份: ${testItem.birthDate.month}`);
            }, 600);

            setTimeout(() => {
                dayField.value = testItem.birthDate.day;
                triggerInputEvent(dayField);
                GM_log(`填写日期: ${testItem.birthDate.day}`);
            }, 800);

            setTimeout(() => {
                yearField.value = testItem.birthDate.year;
                triggerInputEvent(yearField);
                GM_log(`填写年份: ${testItem.birthDate.year}`);
            }, 1000);

            // 填写SSN
            setTimeout(() => {
                ssnField.value = testItem.ssn;
                triggerInputEvent(ssnField);
                GM_log(`填写SSN: ${testItem.ssn}`);
            }, 1200);

            // 提交表单
            setTimeout(() => {
                submitForm();
            }, 2000);

        } catch (error) {
            GM_log('填写表单时出错:', error);
            recordResult(testItem, "ERROR", `填写表单错误: ${error.message}`);
            currentIndex++;
            processNextTest();
        }
    }

    // 触发输入事件
    function triggerInputEvent(element) {
        if (!element) return;

        const inputEvent = new Event('input', { bubbles: true });
        const changeEvent = new Event('change', { bubbles: true });

        element.dispatchEvent(inputEvent);
        element.dispatchEvent(changeEvent);
    }

    // 提交表单
    function submitForm() {
        try {
            GM_log('提交表单...');

            // 查找Continue按钮 - 使用ID选择器
            const continueButton = document.querySelector('#fsa_Button_PersonalInformationContinue');

            if (!continueButton) {
                throw new Error("未找到Continue按钮");
            }

            // 点击Continue按钮
            continueButton.click();

            // 等待响应
            setTimeout(() => {
                checkResult();
            }, 3000);

        } catch (error) {
            GM_log('提交表单时出错:', error);
            recordResult(testData[currentIndex], "ERROR", `提交错误: ${error.message}`);

            currentIndex++;
            processNextTest();
        }
    }

    // 检查结果
    function checkResult() {
        try {
            const testItem = testData[currentIndex];

            // 检查URL和页面内容
            const pageUrl = window.location.href;
            const pageContent = document.body.innerText;

            // 检查是否有错误信息
            const hasError = pageContent.includes("Account already exists");

            if (hasError) {
                GM_log(`账户已存在: ${testItem.firstName} ${testItem.lastName}`);
                recordResult(testItem, "EXISTS", "账户已存在");

                // 继续下一个测试
                currentIndex++;
                processNextTest();
            } else if (pageUrl.includes('account-info')) {
                GM_log(`账户创建成功: ${testItem.firstName} ${testItem.lastName}`);
                recordResult(testItem, "SUCCESS", "可以创建账户");

                // 返回上一步 - 使用正确的ID: fsa_Button_ShowPreviousStep
                const previousButton = document.querySelector('#fsa_Button_ShowPreviousStep');
                if (previousButton) {
                    previousButton.click();

                    setTimeout(() => {
                        currentIndex++;
                        processNextTest();
                    }, 3000);
                } else {
                    // 如果没有找到Previous按钮，尝试通用方法查找
                    const anyPreviousButton = findButtonByText("Previous");
                    if (anyPreviousButton) {
                        anyPreviousButton.click();
                        setTimeout(() => {
                            currentIndex++;
                            processNextTest();
                        }, 3000);
                    } else {
                        // 如果没有Previous按钮，直接继续
                        currentIndex++;
                        setTimeout(() => {
                            processNextTest();
                        }, 1500);
                    }
                }
            } else {
                // 再等待一段时间检查
                setTimeout(() => {
                    checkResultAgain();
                }, 2000);
            }

        } catch (error) {
            GM_log('检查结果时出错:', error);
            recordResult(testData[currentIndex], "ERROR", `检查错误: ${error.message}`);

            currentIndex++;
            processNextTest();
        }
    }

    // 再次检查结果
    function checkResultAgain() {
        try {
            const testItem = testData[currentIndex];

            // 检查URL和页面内容
            const pageUrl = window.location.href;
            const pageContent = document.body.innerText;

            if (pageUrl.includes('account-info')) {
                GM_log(`账户创建成功: ${testItem.firstName} ${testItem.lastName}`);
                recordResult(testItem, "SUCCESS", "可以创建账户");

                // 返回上一步 - 使用正确的ID: fsa_Button_ShowPreviousStep
                const previousButton = document.querySelector('#fsa_Button_ShowPreviousStep');
                if (previousButton) {
                    previousButton.click();

                    setTimeout(() => {
                        currentIndex++;
                        processNextTest();
                    }, 3000);
                } else {
                    // 如果没有找到Previous按钮，尝试通用方法查找
                    const anyPreviousButton = findButtonByText("Previous");
                    if (anyPreviousButton) {
                        anyPreviousButton.click();
                        setTimeout(() => {
                            currentIndex++;
                            processNextTest();
                        }, 3000);
                    } else {
                        currentIndex++;
                        setTimeout(() => {
                            processNextTest();
                        }, 1500);
                    }
                }
            } else {
                const hasError = pageContent.includes("Account already exists");
                if (hasError) {
                    GM_log(`账户已存在: ${testItem.firstName} ${testItem.lastName}`);
                    recordResult(testItem, "EXISTS", "账户已存在");
                } else {
                    GM_log(`未知状态: ${testItem.firstName} ${testItem.lastName}`);
                    recordResult(testItem, "UNKNOWN", "未知状态");
                }

                currentIndex++;
                processNextTest();
            }
        } catch (error) {
            GM_log('再次检查结果时出错:', error);
            recordResult(testData[currentIndex], "ERROR", `检查错误: ${error.message}`);

            currentIndex++;
            processNextTest();
        }
    }

    // 记录测试结果
    function recordResult(testItem, status, message) {
        const result = {
            index: currentIndex + 1,
            firstName: testItem.firstName,
            lastName: testItem.lastName,
            birthDate: `${testItem.birthDate.month}/${testItem.birthDate.day}/${testItem.birthDate.year}`,
            ssn: testItem.ssn,
            status: status,
            message: message,
            timestamp: new Date().toLocaleString()
        };

        testResults.push(result);
        updateResultsDisplay();

        // 保存到本地存储
        GM_setValue('fsa_test_results', JSON.stringify(testResults));

        GM_log('结果已记录:', result);
    }

    // 更新结果显示
    function updateResultsDisplay() {
        const resultsList = $('#results-list');

        if (testResults.length === 0) {
            resultsList.html('暂无结果');
            return;
        }

        let html = '';
        testResults.forEach(result => {
            const statusColor = result.status === 'SUCCESS' ? '#28a745' :
                               result.status === 'EXISTS' ? '#dc3545' : '#6c757d';

            const statusText = result.status === 'SUCCESS' ? '✓ 可注册' :
                              result.status === 'EXISTS' ? '✗ 已存在' : '? 未知';

            html += `
                <div style="margin-bottom: 8px; padding: 5px; background: white; border-radius: 3px; font-size: 12px;">
                    <div><strong>${result.firstName} ${result.lastName}</strong></div>
                    <div>SSN: ${result.ssn}</div>
                    <div style="color: ${statusColor};"><strong>${statusText}</strong> - ${result.message}</div>
                </div>
            `;
        });

        resultsList.html(html);
    }

    // 完成测试
    function completeTesting() {
        isTestingInProgress = false;

        // 清理异步处理状态
        if (isAsyncMode) {
            isAsyncMode = false;
            isPaused = false;
            if (asyncTimer) {
                clearTimeout(asyncTimer);
                asyncTimer = null;
            }
            // 清理所有活跃工作器
            activeWorkers.clear();
            processingQueue = [];
            $('#pause-test').prop('disabled', true);
            $('#pause-test').text('暂停处理'); // 重置按钮文本
        }

        $('#start-test').prop('disabled', false);
        $('#stop-test').prop('disabled', true);
        $('#test-status').text('测试完成');
        $('#current-test-info').hide();

        GM_log('所有测试完成!');
        GM_log('测试结果:', testResults);

        // 显示完成摘要
        showCompletionSummary();
    }

    // 显示完成摘要
    function showCompletionSummary() {
        const successCount = testResults.filter(r => r.status === 'SUCCESS').length;
        const existsCount = testResults.filter(r => r.status === 'EXISTS').length;
        const errorCount = testResults.filter(r => r.status === 'ERROR' || r.status === 'UNKNOWN').length;

        alert(`测试完成！\n\n` +
              `总测试数量: ${testResults.length}\n` +
              `可注册账户: ${successCount}\n` +
              `账户已存在: ${existsCount}\n` +
              `错误/未知: ${errorCount}\n\n` +
              `点击"导出结果"按钮保存详细报告`);
    }

    // 导出结果
    function exportResults() {
        if (testResults.length === 0) {
            alert('暂无测试结果可导出');
            return;
        }

        let csvContent = 'Index,FirstName,LastName,BirthDate,SSN,Status,Message,Timestamp\n';

        testResults.forEach(result => {
            csvContent += `${result.index},"${result.firstName}","${result.lastName}","${result.birthDate}","${result.ssn}","${result.status}","${result.message}","${result.timestamp}"\n`;
        });

        // 创建下载链接
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `FSA_Test_Results_${new Date().toISOString().slice(0,10)}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        GM_log('结果已导出为CSV文件');
    }

    // 查找带有特定文本的按钮
    function findButtonByText(text) {
        // 使用更精确的选择器，基于ID和文本内容
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.find(btn => btn.textContent.includes(text));
    }

    // 加载之前的数据和结果
    function loadPreviousData() {
        // 加载导入的数据
        const savedData = GM_getValue('fsa_imported_data', null);
        if (savedData) {
            try {
                testData = JSON.parse(savedData);
                updateDataCount();
                updateProgressDisplay();

                // 如果有数据，启用开始测试按钮
                if (testData.length > 0) {
                    $('#start-test').prop('disabled', false);
                    $('#test-status').text('待开始');
                }

                GM_log('已加载之前导入的数据:', testData.length, '条');
            } catch (error) {
                GM_log('加载导入数据时出错:', error);
            }
        }

        // 加载测试结果
        const savedResults = GM_getValue('fsa_test_results', null);
        if (savedResults) {
            try {
                testResults = JSON.parse(savedResults);
                updateResultsDisplay();
                GM_log('已加载之前的测试结果:', testResults.length, '条');
            } catch (error) {
                GM_log('加载测试结果时出错:', error);
            }
        }
    }

    // 初始化
    function init() {
        // 延迟创建面板，确保页面加载完成
        setTimeout(() => {
            createControlPanel();
            loadPreviousData();

            // 添加样式
            $('head').append(`
                <style>
                    #fsa-tester-panel button:hover {
                        transform: translateY(-1px);
                        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                        transition: all 0.2s ease;
                    }

                    #fsa-tester-panel button:disabled {
                        opacity: 0.6;
                        cursor: not-allowed;
                        transform: none !important;
                    }

                    #results-summary::-webkit-scrollbar {
                        width: 6px;
                    }

                    #results-summary::-webkit-scrollbar-track {
                        background: rgba(0,0,0,0.1);
                        border-radius: 3px;
                    }

                    #results-summary::-webkit-scrollbar-thumb {
                        background: rgba(0,0,0,0.2);
                        border-radius: 3px;
                    }

                    #results-summary::-webkit-scrollbar-thumb:hover {
                        background: rgba(0,0,0,0.3);
                    }
                </style>
            `);
        }, 1500);
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();