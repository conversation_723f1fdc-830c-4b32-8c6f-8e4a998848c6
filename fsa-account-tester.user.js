// ==UserScript==
// @name         FSA账户测试器
// @namespace    http://tampermonkey.net/
// @version      2.2
// @description  自动测试FSA账户注册状态 - 支持多种数据格式，自动处理姓氏空格，智能弹窗处理
// <AUTHOR>
// @match        https://studentaid.gov/fsa-id/create-account/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_log
// @require      https://code.jquery.com/jquery-3.6.0.min.js
// ==/UserScript==

(function() {
    'use strict';

    // 测试数据 - 现在支持导入自定义数据
    let testData = [];

    // 测试结果存储
    let testResults = [];
    let currentIndex = 0;
    let isTestingInProgress = false;
    let retryCount = 0;
    const maxRetries = 3;

    // 性能统计
    let performanceStats = {
        startTime: null,
        lastTestTime: null,
        averageTimePerTest: 0,
        totalProcessed: 0,
        estimatedTimeRemaining: 0,
        networkDelay: 0,
        lastNetworkCheck: null
    };

    // 网络延迟检测
    function checkNetworkDelay() {
        const startTime = Date.now();

        // 发送一个小的请求来测试网络延迟
        fetch(window.location.href, {
            method: 'HEAD',
            cache: 'no-cache'
        }).then(() => {
            const delay = Date.now() - startTime;
            performanceStats.networkDelay = delay;
            performanceStats.lastNetworkCheck = Date.now();
            GM_log(`📡 网络延迟: ${delay}ms`);
        }).catch(() => {
            performanceStats.networkDelay = 1000; // 默认1秒延迟
            GM_log(`📡 网络检测失败，使用默认延迟`);
        });
    }

    // 精确检测Account Information页面元素
    function isAccountInfoPage() {
        try {
            const url = window.location.href;
            if (!url.includes('account-info')) {
                return false;
            }

            // 检查页面标题
            const titleElements = Array.from(document.querySelectorAll('h1, h2, h3, .page-title, .title'))
                .filter(el => el.textContent && el.textContent.includes('Account Information'));

            // 检查关键表单字段
            const formElements = [
                document.querySelector('input[type="text"]'), // Username
                document.querySelector('input[type="email"]'), // Email
                document.querySelector('input[type="password"]'), // Password
                document.querySelector('input[placeholder*="Username"]'),
                document.querySelector('input[placeholder*="Email"]'),
                document.querySelector('input[placeholder*="Password"]')
            ].filter(el => el !== null);

            // 检查Previous按钮
            const previousButton = document.querySelector('#fsa_Button_ShowPreviousStep') ||
                                 findButtonByText("Previous");

            const hasTitle = titleElements.length > 0;
            const hasFormFields = formElements.length >= 2; // 至少有2个表单字段
            const hasPreviousButton = previousButton !== null;

            GM_log(`🔍 Account Info页面检测: 标题=${hasTitle}, 表单字段=${formElements.length}, Previous按钮=${hasPreviousButton}`);

            return hasTitle || (hasFormFields && hasPreviousButton);
        } catch (error) {
            GM_log('Account Info页面检测出错:', error);
            return false;
        }
    }

    // 检测错误信息元素
    function hasAccountExistsError() {
        try {
            // 方法1: 检查特定的错误元素
            const errorSelectors = [
                '.error-message',
                '.alert-danger',
                '.alert-error',
                '[role="alert"]',
                '.validation-error',
                '.field-error'
            ];

            for (let selector of errorSelectors) {
                const elements = document.querySelectorAll(selector);
                for (let element of elements) {
                    if (element.textContent && element.textContent.includes('Account already exists')) {
                        GM_log(`🚨 错误元素检测到: ${selector} - ${element.textContent.trim()}`);
                        return true;
                    }
                }
            }

            // 方法2: 检查所有包含错误文本的元素
            const allElements = document.querySelectorAll('*');
            for (let element of allElements) {
                if (element.textContent &&
                    (element.textContent.includes('Account already exists') ||
                     element.textContent.includes('already exists') ||
                     element.textContent.includes('Account exists'))) {

                    // 确保不是隐藏元素
                    const style = window.getComputedStyle(element);
                    if (style.display !== 'none' && style.visibility !== 'hidden') {
                        GM_log(`🚨 文本错误检测到: ${element.tagName} - ${element.textContent.trim().substring(0, 100)}`);
                        return true;
                    }
                }
            }

            return false;
        } catch (error) {
            GM_log('错误检测出错:', error);
            return false;
        }
    }

    // 解析导入的数据 - 支持多种直接粘贴格式
    function parseImportedData(text) {
        const lines = text.trim().split('\n');
        const parsedData = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue; // 跳过空行

            // 尝试多种分隔符：TAB、逗号、多个空格、单个空格
            let parts = [];

            // 优先尝试TAB分隔（保持向后兼容）
            if (line.includes('\t')) {
                parts = line.split('\t');
            }
            // 尝试逗号分隔
            else if (line.includes(',')) {
                parts = line.split(',');
            }
            // 尝试多个空格分隔
            else if (line.includes('  ')) {
                parts = line.split(/\s{2,}/); // 匹配2个或更多空格
            }
            // 最后尝试单个空格分隔
            else {
                parts = line.split(/\s+/); // 匹配一个或多个空格
            }

            // 清理数据，移除多余的空白和引号
            parts = parts.map(part => part.trim().replace(/^["']|["']$/g, ''));

            if (parts.length >= 4) {
                const firstName = parts[0].trim();
                let lastName = parts[1].trim();
                const birthDateStr = parts[2].trim(); // 支持多种日期格式
                const ssn = parts[3].trim();

                // 处理姓氏中的空格 - 只保留最后一个部分
                if (lastName.includes(' ')) {
                    const lastNameParts = lastName.split(/\s+/);
                    const originalLastName = lastName;
                    lastName = lastNameParts[lastNameParts.length - 1]; // 只保留最后一个部分
                    console.log(`姓氏处理: "${originalLastName}" → "${lastName}"`);
                }

                // 解析出生日期 - 支持多种格式
                const birthDate = parseBirthDate(birthDateStr);

                if (birthDate) {
                    parsedData.push({
                        firstName: firstName,
                        lastName: lastName,
                        birthDate: birthDate,
                        ssn: ssn
                    });
                } else {
                    console.warn(`第 ${i + 1} 行日期格式错误: ${birthDateStr}`);
                }
            } else {
                console.warn(`第 ${i + 1} 行数据格式错误 (需要至少4个字段): ${line}`);
            }
        }

        return parsedData;
    }

    // 解析出生日期 - 支持多种格式
    function parseBirthDate(dateStr) {
        if (!dateStr) return null;

        // 移除所有非数字和分隔符的字符
        const cleanDate = dateStr.replace(/[^\d\/\-\.]/g, '');

        let dateParts = [];

        // 尝试不同的分隔符
        if (cleanDate.includes('/')) {
            dateParts = cleanDate.split('/');
        } else if (cleanDate.includes('-')) {
            dateParts = cleanDate.split('-');
        } else if (cleanDate.includes('.')) {
            dateParts = cleanDate.split('.');
        } else if (cleanDate.length === 8) {
            // 尝试解析MMDDYYYY或YYYYMMDD格式
            if (cleanDate.substring(0, 4) > '1900') {
                // YYYYMMDD格式
                let year = cleanDate.substring(0, 4);
                let month = cleanDate.substring(4, 6);
                let day = cleanDate.substring(6, 8);

                // 处理日期为00的情况，自动设为01
                if (day === '00') {
                    day = '01';
                    console.log(`日期处理: ${dateStr} 中的日期00已自动调整为01`);
                }

                dateParts = [month, day, year];
            } else {
                // MMDDYYYY格式
                dateParts = [cleanDate.substring(0, 2), cleanDate.substring(2, 4), cleanDate.substring(4, 8)];
            }
        }

        if (dateParts.length === 3) {
            let month, day, year;

            // 判断日期格式 (MM/DD/YYYY 或 DD/MM/YYYY 或 YYYY/MM/DD)
            if (dateParts[0].length === 4) {
                // YYYY/MM/DD格式
                year = dateParts[0];
                month = dateParts[1];
                day = dateParts[2];
            } else if (parseInt(dateParts[0]) > 12) {
                // 第一个数字大于12，可能是DD/MM/YYYY格式
                day = dateParts[0];
                month = dateParts[1];
                year = dateParts[2];
            } else {
                // 默认MM/DD/YYYY格式
                month = dateParts[0];
                day = dateParts[1];
                year = dateParts[2];
            }

            // 再次检查日期为00的情况
            if (day === '00') {
                day = '01';
                console.log(`日期处理: 日期字段00已自动调整为01`);
            }

            return {
                month: month.padStart(2, '0'),
                day: day.padStart(2, '0'),
                year: year
            };
        }

        return null;
    }

    // 创建控制面板 - 美化简洁版
    function createControlPanel() {
        const panel = $(`
            <div id="fsa-tester-panel" style="
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 16px;
                padding: 0;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                overflow: hidden;
                backdrop-filter: blur(10px);
            ">
                <!-- 头部 -->
                <div style="
                    background: rgba(255,255,255,0.1);
                    padding: 16px 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border-bottom: 1px solid rgba(255,255,255,0.2);
                ">
                    <div style="display: flex; align-items: center;">
                        <div style="
                            width: 8px;
                            height: 8px;
                            background: #4ade80;
                            border-radius: 50%;
                            margin-right: 8px;
                            animation: pulse 2s infinite;
                        "></div>
                        <h3 style="margin: 0; color: white; font-size: 16px; font-weight: 600;">🎯 FSA测试器</h3>
                    </div>
                    <button id="minimize-panel" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        width: 28px;
                        height: 28px;
                        border-radius: 50%;
                        cursor: pointer;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.2s;
                    " onmouseover="this.style.background='rgba(255,255,255,0.3)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'">−</button>
                </div>

                <div id="panel-content" style="background: white; padding: 20px;">
                    <!-- 数据导入区域 -->
                    <div style="margin-bottom: 20px;">
                        <div style="
                            display: flex;
                            align-items: center;
                            margin-bottom: 12px;
                            color: #374151;
                            font-weight: 600;
                        ">
                            📁 数据导入
                            <span id="data-count-badge" style="
                                background: #e5e7eb;
                                color: #6b7280;
                                padding: 2px 8px;
                                border-radius: 12px;
                                font-size: 11px;
                                margin-left: 8px;
                            ">${testData.length} 条</span>
                        </div>
                        <textarea id="data-input" placeholder="粘贴数据：SAMANTHA STONE 07/21/1998 231811389" style="
                            width: 100%;
                            height: 60px;
                            padding: 12px;
                            border: 2px solid #e5e7eb;
                            border-radius: 8px;
                            margin-bottom: 12px;
                            font-family: 'Consolas', monospace;
                            font-size: 11px;
                            resize: none;
                            transition: border-color 0.2s;
                            background: #f9fafb;
                        " onfocus="this.style.borderColor='#667eea'; this.style.background='white';" onblur="this.style.borderColor='#e5e7eb'; this.style.background='#f9fafb';"></textarea>
                        <div style="display: flex; gap: 8px;">
                            <button id="import-data" style="
                                background: linear-gradient(135deg, #10b981, #059669);
                                color: white;
                                border: none;
                                padding: 8px 16px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 12px;
                                font-weight: 500;
                                flex: 1;
                                transition: transform 0.1s;
                            " onmousedown="this.style.transform='scale(0.98)'" onmouseup="this.style.transform='scale(1)'">📥 导入</button>
                            <button id="clear-data" style="
                                background: #6b7280;
                                color: white;
                                border: none;
                                padding: 8px 12px;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 12px;
                                transition: transform 0.1s;
                            " onmousedown="this.style.transform='scale(0.98)'" onmouseup="this.style.transform='scale(1)'">🗑️</button>
                        </div>
                    </div>

                    <!-- 状态信息 -->
                    <div style="
                        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
                        padding: 16px;
                        border-radius: 12px;
                        margin-bottom: 20px;
                    ">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 12px;">
                            <div>
                                <div style="color: #6b7280; margin-bottom: 4px;">进度</div>
                                <div style="font-weight: 600; color: #374151;" id="current-progress">0/${testData.length}</div>
                            </div>
                            <div>
                                <div style="color: #6b7280; margin-bottom: 4px;">重试</div>
                                <div style="font-weight: 600; color: #374151;" id="retry-count">0/${maxRetries}</div>
                            </div>
                        </div>
                        <div style="margin-top: 12px;">
                            <div style="color: #6b7280; margin-bottom: 4px; font-size: 12px;">状态</div>
                            <div style="
                                font-weight: 600;
                                color: #374151;
                                font-size: 13px;
                            " id="test-status">${testData.length > 0 ? '待开始' : '待导入数据'}</div>
                        </div>
                    </div>

                    <!-- 控制按钮 -->
                    <div style="display: flex; gap: 8px; margin-bottom: 20px;">
                        <button id="start-test" style="
                            background: linear-gradient(135deg, #10b981, #059669);
                            color: white;
                            border: none;
                            padding: 12px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            flex: 1;
                            transition: all 0.2s;
                        " disabled onmouseover="if(!this.disabled) this.style.transform='translateY(-1px)'" onmouseout="this.style.transform='translateY(0)'">▶️ 开始</button>

                        <button id="stop-test" style="
                            background: linear-gradient(135deg, #ef4444, #dc2626);
                            color: white;
                            border: none;
                            padding: 12px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            flex: 1;
                            transition: all 0.2s;
                        " disabled onmouseover="if(!this.disabled) this.style.transform='translateY(-1px)'" onmouseout="this.style.transform='translateY(0)'">⏹️ 停止</button>

                        <button id="export-results" style="
                            background: linear-gradient(135deg, #3b82f6, #2563eb);
                            color: white;
                            border: none;
                            padding: 12px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            width: 48px;
                            transition: all 0.2s;
                        " onmouseover="this.style.transform='translateY(-1px)'" onmouseout="this.style.transform='translateY(0)'">📊</button>
                    </div>

                    <!-- 当前测试信息 -->
                    <div id="current-test-info" style="
                        background: linear-gradient(135deg, #fef3c7, #fde68a);
                        padding: 12px;
                        border-radius: 8px;
                        margin-bottom: 16px;
                        display: none;
                        border-left: 4px solid #f59e0b;
                    ">
                        <div style="font-size: 12px; color: #92400e; margin-bottom: 6px; font-weight: 600;">🔄 当前测试</div>
                        <div id="current-name" style="font-size: 11px; color: #78350f;"></div>
                        <div id="current-ssn" style="font-size: 11px; color: #78350f;"></div>
                    </div>

                    <!-- 结果摘要 -->
                    <div id="results-summary" style="
                        background: #f8fafc;
                        border: 1px solid #e2e8f0;
                        border-radius: 8px;
                        max-height: 120px;
                        overflow: hidden;
                    ">
                        <div style="
                            padding: 12px;
                            border-bottom: 1px solid #e2e8f0;
                            font-weight: 600;
                            color: #374151;
                            font-size: 12px;
                            background: #f1f5f9;
                        ">📋 测试结果</div>
                        <div id="results-list" style="
                            padding: 12px;
                            font-size: 11px;
                            color: #6b7280;
                            max-height: 80px;
                            overflow-y: auto;
                        ">暂无结果</div>
                    </div>
                </div>
            </div>
        `);

        $('body').append(panel);

        // 最小化功能
        $('#minimize-panel').click(function() {
            const content = $('#panel-content');
            const button = $(this);
            if (content.is(':visible')) {
                content.hide();
                button.text('+');
            } else {
                content.show();
                button.text('−');
            }
        });

        // 绑定事件
        $('#start-test').click(startTesting);
        $('#stop-test').click(stopTesting);
        $('#export-results').click(exportResults);
        $('#import-data').click(importData);
        $('#clear-data').click(clearData);
    }

    // 导入数据 - 优化版，无确认对话框
    function importData() {
        const dataInput = document.getElementById('data-input');
        const text = dataInput.value.trim();

        if (!text) {
            showNotification('请先粘贴数据到文本框中', 'warning');
            return;
        }

        try {
            const newData = parseImportedData(text);

            if (newData.length === 0) {
                showNotification('未能解析到有效数据，请检查格式', 'error');
                return;
            }

            testData = newData;
            updateDataCount();
            updateProgressDisplay();

            // 启用开始测试按钮
            $('#start-test').prop('disabled', false);
            $('#test-status').text('✅ 准备就绪');

            // 显示成功通知
            showNotification(`🎉 成功导入 ${newData.length} 条数据`, 'success');
            GM_log('导入数据:', testData);

            // 清空输入框
            dataInput.value = '';

            // 保存导入的数据
            GM_setValue('fsa_imported_data', JSON.stringify(testData));

        } catch (error) {
            showNotification('数据解析错误: ' + error.message, 'error');
            GM_log('导入数据错误:', error);
        }
    }

    // 显示通知 - 替代alert的美观通知
    function showNotification(message, type = 'info') {
        // 移除已存在的通知
        $('.fsa-notification').remove();

        const colors = {
            success: { bg: '#10b981', icon: '✅' },
            error: { bg: '#ef4444', icon: '❌' },
            warning: { bg: '#f59e0b', icon: '⚠️' },
            info: { bg: '#3b82f6', icon: 'ℹ️' }
        };

        const color = colors[type] || colors.info;

        const notification = $(`
            <div class="fsa-notification" style="
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: ${color.bg};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10001;
                font-family: 'Segoe UI', sans-serif;
                font-size: 14px;
                font-weight: 500;
                animation: slideDown 0.3s ease-out;
            ">
                ${color.icon} ${message}
            </div>
        `);

        $('body').append(notification);

        // 3秒后自动消失
        setTimeout(() => {
            notification.fadeOut(300, () => notification.remove());
        }, 3000);
    }

    // 清空数据 - 优化版
    function clearData() {
        if (isTestingInProgress) {
            showNotification('测试进行中，无法清空数据', 'warning');
            return;
        }

        if (testData.length === 0) {
            showNotification('当前没有数据', 'info');
            return;
        }

        // 使用美观的确认对话框
        showConfirmDialog(
            '确定要清空所有数据吗？',
            '这将删除所有已导入的测试数据和结果',
            () => {
                testData = [];
                testResults = [];
                currentIndex = 0;

                updateDataCount();
                updateProgressDisplay();
                updateResultsDisplay();

                // 禁用开始测试按钮
                $('#start-test').prop('disabled', true);
                $('#test-status').text('待导入数据');

                // 清空文本输入
                document.getElementById('data-input').value = '';

                // 清除保存的数据
                GM_setValue('fsa_imported_data', null);
                GM_setValue('fsa_test_results', null);

                showNotification('🗑️ 数据已清空', 'success');
            }
        );
    }

    // 美观的确认对话框
    function showConfirmDialog(title, message, onConfirm) {
        // 移除已存在的对话框
        $('.fsa-confirm-dialog').remove();

        const dialog = $(`
            <div class="fsa-confirm-dialog" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 10002;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.2s ease-out;
            ">
                <div style="
                    background: white;
                    border-radius: 12px;
                    padding: 24px;
                    max-width: 400px;
                    width: 90%;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                    animation: slideUp 0.3s ease-out;
                ">
                    <div style="
                        font-size: 18px;
                        font-weight: 600;
                        color: #374151;
                        margin-bottom: 8px;
                    ">${title}</div>
                    <div style="
                        color: #6b7280;
                        margin-bottom: 20px;
                        line-height: 1.5;
                    ">${message}</div>
                    <div style="
                        display: flex;
                        gap: 12px;
                        justify-content: flex-end;
                    ">
                        <button class="cancel-btn" style="
                            background: #f3f4f6;
                            color: #374151;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                            transition: all 0.2s;
                        ">取消</button>
                        <button class="confirm-btn" style="
                            background: #ef4444;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                            transition: all 0.2s;
                        ">确定</button>
                    </div>
                </div>
            </div>
        `);

        $('body').append(dialog);

        // 绑定事件
        dialog.find('.cancel-btn').click(() => {
            dialog.fadeOut(200, () => dialog.remove());
        });

        dialog.find('.confirm-btn').click(() => {
            dialog.fadeOut(200, () => dialog.remove());
            onConfirm();
        });

        // 点击背景关闭
        dialog.click((e) => {
            if (e.target === dialog[0]) {
                dialog.fadeOut(200, () => dialog.remove());
            }
        });
    }

    // 更新数据计数显示
    function updateDataCount() {
        $('#data-count').text(testData.length);
        $('#data-count-badge').text(`${testData.length} 条`);

        // 更新徽章颜色
        const badge = $('#data-count-badge');
        if (testData.length === 0) {
            badge.css({
                'background': '#e5e7eb',
                'color': '#6b7280'
            });
        } else {
            badge.css({
                'background': '#dcfce7',
                'color': '#166534'
            });
        }
    }

    // 更新进度显示
    function updateProgressDisplay() {
        $('#current-progress').text(`${currentIndex}/${testData.length}`);
    }

    // 开始测试
    function startTesting() {
        if (isTestingInProgress) return;

        // 检查是否有测试数据
        if (testData.length === 0) {
            alert('请先导入测试数据');
            return;
        }

        isTestingInProgress = true;
        currentIndex = 0;
        testResults = [];

        // 重置性能统计
        performanceStats = {
            startTime: null,
            lastTestTime: null,
            averageTimePerTest: 0,
            totalProcessed: 0,
            estimatedTimeRemaining: 0
        };

        $('#start-test').prop('disabled', true);
        $('#stop-test').prop('disabled', false);
        $('#test-status').text('🎯 精确测试中...');
        $('#current-test-info').show();

        GM_log('🎯 开始FSA账户精确测试...');
        GM_log('📊 测试数据量:', testData.length);
        GM_log('🔍 已启用元素精确检测模式');

        // 检测网络延迟
        checkNetworkDelay();

        // 检查当前页面
        if (!window.location.href.includes('personal-info')) {
            navigateToPersonalInfo();
        } else {
            processNextTest();
        }
    }

    // 导航到个人信息页面
    function navigateToPersonalInfo() {
        GM_log('导航到个人信息页面...');

        // 如果在启动页面，点击Get Started
        if (window.location.href.includes('launch')) {
            const getStartedButton = findButtonByText("Get Started");

            if (getStartedButton) {
                GM_log('点击Get Started按钮...');
                getStartedButton.click();

                // 等待页面加载
                setTimeout(() => {
                    if (window.location.href.includes('personal-info')) {
                        GM_log('已进入个人信息页面');
                        processNextTest();
                    } else {
                        GM_log('导航失败，重试...');
                        retryCount++;
                        if (retryCount < maxRetries) {
                            setTimeout(navigateToPersonalInfo, 2000);
                        } else {
                            recordResult({firstName: "导航", lastName: "错误"}, "ERROR", "无法导航到个人信息页面");
                            stopTesting();
                        }
                    }
                }, 3000);
            } else {
                GM_log('未找到Get Started按钮');
                recordResult({firstName: "导航", lastName: "错误"}, "ERROR", "未找到Get Started按钮");
                stopTesting();
            }
        } else {
            // 直接导航到个人信息页面
            window.location.href = "https://studentaid.gov/fsa-id/create-account/personal-info";

            // 等待页面加载
            setTimeout(() => {
                processNextTest();
            }, 3000);
        }
    }

    // 停止测试
    function stopTesting() {
        isTestingInProgress = false;

        $('#start-test').prop('disabled', false);
        $('#stop-test').prop('disabled', true);
        $('#test-status').text('已停止');
        $('#current-test-info').hide();

        GM_log('测试已停止');
    }

    // 处理下一个测试
    function processNextTest() {
        if (!isTestingInProgress || currentIndex >= testData.length) {
            completeTesting();
            return;
        }

        // 性能统计
        const currentTime = Date.now();
        if (performanceStats.startTime === null) {
            performanceStats.startTime = currentTime;
        }

        if (performanceStats.lastTestTime !== null) {
            const timeDiff = currentTime - performanceStats.lastTestTime;
            performanceStats.totalProcessed++;
            performanceStats.averageTimePerTest =
                (performanceStats.averageTimePerTest * (performanceStats.totalProcessed - 1) + timeDiff) / performanceStats.totalProcessed;

            const remaining = testData.length - currentIndex;
            performanceStats.estimatedTimeRemaining = (performanceStats.averageTimePerTest * remaining) / 1000;
        }
        performanceStats.lastTestTime = currentTime;

        const testItem = testData[currentIndex];
        updateCurrentTestInfo(testItem);

        GM_log(`⚡ 快速测试第 ${currentIndex + 1}/${testData.length} 条数据: ${testItem.firstName} ${testItem.lastName}`);

        // 显示性能信息
        if (performanceStats.totalProcessed > 0) {
            const avgSeconds = (performanceStats.averageTimePerTest / 1000).toFixed(1);
            const etaMinutes = (performanceStats.estimatedTimeRemaining / 60).toFixed(1);
            GM_log(`📊 平均速度: ${avgSeconds}秒/条, 预计剩余: ${etaMinutes}分钟`);
        }

        // 重置重试计数
        retryCount = 0;

        // 填写表单
        fillFormWithRetry(testItem);
    }

    // 更新当前测试信息
    function updateCurrentTestInfo(testItem) {
        updateProgressDisplay();
        $('#current-name').text(`姓名: ${testItem.firstName} ${testItem.lastName}`);
        $('#current-ssn').text(`SSN: ${testItem.ssn} (出生: ${testItem.birthDate.month}/${testItem.birthDate.day}/${testItem.birthDate.year})`);
        $('#retry-count').text(`${retryCount}/${maxRetries}`);

        // 显示性能信息
        if (performanceStats.totalProcessed > 0) {
            const avgSeconds = (performanceStats.averageTimePerTest / 1000).toFixed(1);
            const etaMinutes = (performanceStats.estimatedTimeRemaining / 60).toFixed(1);
            const networkStatus = performanceStats.networkDelay > 0 ? `网络${performanceStats.networkDelay}ms` : '检测中';
            $('#test-status').text(`🎯 精确测试中... (${avgSeconds}秒/条, ${networkStatus}, 剩余${etaMinutes}分钟)`);
        } else {
            $('#test-status').text(`🎯 精确测试中... (元素检测模式)`);
        }
    }

    // 带重试的表单填写
    function fillFormWithRetry(testItem) {
        // 检查必要元素是否存在
        // 使用MCP环境中观察到的实际ID和选择器
        const firstNameField = document.querySelector('#fsa_Input_FirstName');
        const lastNameField = document.querySelector('#fsa_Input_LastName');
        const monthField = document.querySelector('#fsa_Input_DateOfBirthMonth');
        const dayField = document.querySelector('#fsa_Input_DateOfBirthDay');
        const yearField = document.querySelector('#fsa_Input_DateOfBirthYear');
        const ssnField = document.querySelector('#fsa_Input_Ssn');

        if (!firstNameField || !lastNameField || !monthField || !dayField || !yearField || !ssnField) {
            if (retryCount < maxRetries) {
                retryCount++;
                GM_log(`表单元素未找到，重试 ${retryCount}/${maxRetries}`);
                $('#retry-count').text(`${retryCount}/${maxRetries}`);
                setTimeout(() => fillFormWithRetry(testItem), 2000);
                return;
            } else {
                GM_log('表单元素未找到，跳过此项');
                recordResult(testItem, "ERROR", '表单元素未找到');
                currentIndex++;
                processNextTest();
                return;
            }
        }

        fillForm(testItem);
    }

    // 填写表单 - 优化版本，大幅提升速度
    function fillForm(testItem) {
        try {
            GM_log('快速填写表单...');

            // 获取表单元素 - 使用ID选择器
            const firstNameField = document.querySelector('#fsa_Input_FirstName');
            const lastNameField = document.querySelector('#fsa_Input_LastName');
            const monthField = document.querySelector('#fsa_Input_DateOfBirthMonth');
            const dayField = document.querySelector('#fsa_Input_DateOfBirthDay');
            const yearField = document.querySelector('#fsa_Input_DateOfBirthYear');
            const ssnField = document.querySelector('#fsa_Input_Ssn');

            // 快速清空并填写所有字段
            const fields = [
                { element: firstNameField, value: testItem.firstName, name: '姓名' },
                { element: lastNameField, value: testItem.lastName, name: '姓氏' },
                { element: monthField, value: testItem.birthDate.month, name: '月份' },
                { element: dayField, value: testItem.birthDate.day, name: '日期' },
                { element: yearField, value: testItem.birthDate.year, name: '年份' },
                { element: ssnField, value: testItem.ssn, name: 'SSN' }
            ];

            // 批量处理所有字段，保持合理间隔确保稳定性
            fields.forEach((field, index) => {
                if (field.element) {
                    // 清空
                    field.element.value = '';
                    triggerInputEvent(field.element);

                    // 合理的填写间隔，每个字段延迟100ms
                    setTimeout(() => {
                        field.element.value = field.value;
                        triggerInputEvent(field.element);
                        GM_log(`填写${field.name}: ${field.value}`);
                    }, (index + 1) * 100);
                }
            });

            // 适中的提交等待时间：确保所有字段都已填写完成
            setTimeout(() => {
                submitForm();
            }, 1000);

        } catch (error) {
            GM_log('填写表单时出错:', error);
            recordResult(testItem, "ERROR", `填写表单错误: ${error.message}`);
            currentIndex++;
            processNextTest();
        }
    }

    // 智能检测页面是否完全加载
    function isPageFullyLoaded() {
        try {
            // 检查基本加载状态
            if (document.readyState !== 'complete') {
                return false;
            }

            // 检查是否有加载指示器
            const loadingIndicators = [
                '.loading',
                '.spinner',
                '[aria-busy="true"]',
                '.progress',
                '.loader'
            ];

            for (let selector of loadingIndicators) {
                const element = document.querySelector(selector);
                if (element && element.style.display !== 'none') {
                    return false;
                }
            }

            // 检查页面内容是否有意义（不是空白或错误页面）
            const bodyText = document.body.innerText.trim();
            if (bodyText.length < 100) {
                return false;
            }

            // 检查是否有关键元素存在
            const currentUrl = window.location.href;
            if (currentUrl.includes('personal-info')) {
                // 在个人信息页面，检查表单是否存在
                const form = document.querySelector('#fsa_Input_FirstName');
                return form !== null;
            } else if (currentUrl.includes('account-info')) {
                // 在账户信息页面，检查用户名字段是否存在
                const usernameField = document.querySelector('input[type="text"]');
                return usernameField !== null;
            }

            return true;
        } catch (error) {
            GM_log('页面加载检测出错:', error);
            return true; // 出错时假设已加载完成
        }
    }

    // 触发输入事件 - 优化版本，更全面的事件触发
    function triggerInputEvent(element) {
        if (!element) return;

        // 触发更全面的事件序列，确保Angular应用响应
        const events = [
            new Event('focus', { bubbles: true }),
            new Event('input', { bubbles: true }),
            new Event('change', { bubbles: true }),
            new Event('blur', { bubbles: true })
        ];

        events.forEach(event => {
            element.dispatchEvent(event);
        });

        // 额外触发Angular特定事件
        if (window.angular) {
            element.dispatchEvent(new Event('keyup', { bubbles: true }));
            element.dispatchEvent(new Event('keydown', { bubbles: true }));
        }
    }

    // 提交表单
    function submitForm() {
        try {
            GM_log('提交表单...');

            // 查找Continue按钮 - 使用ID选择器
            const continueButton = document.querySelector('#fsa_Button_PersonalInformationContinue');

            if (!continueButton) {
                throw new Error("未找到Continue按钮");
            }

            // 点击Continue按钮
            continueButton.click();

            // 智能等待：给网络足够时间响应
            setTimeout(() => {
                checkResult();
            }, 2500);

        } catch (error) {
            GM_log('提交表单时出错:', error);
            recordResult(testData[currentIndex], "ERROR", `提交错误: ${error.message}`);

            currentIndex++;
            processNextTest();
        }
    }

    // 检查结果 - 使用专用检测函数的精确版本
    function checkResult() {
        try {
            const testItem = testData[currentIndex];
            const pageUrl = window.location.href;

            GM_log(`🔍 精确检查结果中... URL: ${pageUrl}`);

            // 优先检查错误信息（最常见情况）
            if (hasAccountExistsError()) {
                GM_log(`✅ 精确检测：账户已存在 - ${testItem.firstName} ${testItem.lastName}`);
                recordResult(testItem, "EXISTS", "账户已存在");
                currentIndex++;
                processNextTest();
                return;
            }

            // 检查是否成功跳转到Account Information页面
            if (isAccountInfoPage()) {
                GM_log(`✅ 精确检测：账户创建成功 - ${testItem.firstName} ${testItem.lastName}`);
                recordResult(testItem, "SUCCESS", "可以创建账户");

                // 查找Previous按钮返回
                const previousButton = document.querySelector('#fsa_Button_ShowPreviousStep') ||
                                     findButtonByText("Previous");

                if (previousButton) {
                    GM_log('🔙 点击Previous按钮返回...');
                    previousButton.click();
                    setTimeout(() => {
                        currentIndex++;
                        processNextTest();
                    }, 2000);
                } else {
                    GM_log('⚠️ 未找到Previous按钮，直接继续');
                    currentIndex++;
                    setTimeout(() => {
                        processNextTest();
                    }, 1500);
                }
                return;
            }

            // 如果还没有明确结果，等待后再次检查
            GM_log(`⏳ 未检测到明确结果，1.5秒后重试...`);
            setTimeout(() => {
                checkResultAgain();
            }, 1500);

        } catch (error) {
            GM_log('检查结果时出错:', error);
            recordResult(testData[currentIndex], "ERROR", `检查错误: ${error.message}`);
            currentIndex++;
            processNextTest();
        }
    }

    // 再次检查结果 - 使用专用检测函数的精确版本
    function checkResultAgain() {
        try {
            const testItem = testData[currentIndex];
            const pageUrl = window.location.href;

            GM_log(`🔍 再次精确检查结果... URL: ${pageUrl}`);

            // 使用专用函数检查Account Information页面
            if (isAccountInfoPage()) {
                GM_log(`✅ 精确确认：账户创建成功 - ${testItem.firstName} ${testItem.lastName}`);
                recordResult(testItem, "SUCCESS", "可以创建账户");

                // 查找Previous按钮
                const previousButton = document.querySelector('#fsa_Button_ShowPreviousStep') ||
                                     findButtonByText("Previous");

                if (previousButton) {
                    GM_log('🔙 点击Previous按钮返回...');
                    previousButton.click();
                    setTimeout(() => {
                        currentIndex++;
                        processNextTest();
                    }, 1800);
                } else {
                    GM_log('⚠️ 未找到Previous按钮，直接继续');
                    currentIndex++;
                    setTimeout(() => {
                        processNextTest();
                    }, 1200);
                }
                return;
            }

            // 使用专用函数检查错误信息
            if (hasAccountExistsError()) {
                GM_log(`✅ 精确确认：账户已存在 - ${testItem.firstName} ${testItem.lastName}`);
                recordResult(testItem, "EXISTS", "账户已存在");
                currentIndex++;
                processNextTest();
                return;
            }

            // 如果都没有检测到，记录为未知状态
            GM_log(`❓ 状态未明：${testItem.firstName} ${testItem.lastName} - URL: ${pageUrl}`);
            recordResult(testItem, "UNKNOWN", `未知状态 - URL: ${pageUrl}`);

            currentIndex++;
            processNextTest();

        } catch (error) {
            GM_log('再次检查结果时出错:', error);
            recordResult(testData[currentIndex], "ERROR", `检查错误: ${error.message}`);
            currentIndex++;
            processNextTest();
        }
    }

    // 记录测试结果
    function recordResult(testItem, status, message) {
        const result = {
            index: currentIndex + 1,
            firstName: testItem.firstName,
            lastName: testItem.lastName,
            birthDate: `${testItem.birthDate.month}/${testItem.birthDate.day}/${testItem.birthDate.year}`,
            ssn: testItem.ssn,
            status: status,
            message: message,
            timestamp: new Date().toLocaleString()
        };

        testResults.push(result);
        updateResultsDisplay();

        // 保存到本地存储
        GM_setValue('fsa_test_results', JSON.stringify(testResults));

        GM_log('结果已记录:', result);
    }

    // 更新结果显示
    function updateResultsDisplay() {
        const resultsList = $('#results-list');

        if (testResults.length === 0) {
            resultsList.html('暂无结果');
            return;
        }

        let html = '';
        testResults.forEach(result => {
            const statusColor = result.status === 'SUCCESS' ? '#28a745' :
                               result.status === 'EXISTS' ? '#dc3545' : '#6c757d';

            const statusText = result.status === 'SUCCESS' ? '✓ 可注册' :
                              result.status === 'EXISTS' ? '✗ 已存在' : '? 未知';

            html += `
                <div style="margin-bottom: 8px; padding: 5px; background: white; border-radius: 3px; font-size: 12px;">
                    <div><strong>${result.firstName} ${result.lastName}</strong></div>
                    <div>SSN: ${result.ssn}</div>
                    <div style="color: ${statusColor};"><strong>${statusText}</strong> - ${result.message}</div>
                </div>
            `;
        });

        resultsList.html(html);
    }

    // 完成测试
    function completeTesting() {
        isTestingInProgress = false;

        $('#start-test').prop('disabled', false);
        $('#stop-test').prop('disabled', true);
        $('#test-status').text('测试完成');
        $('#current-test-info').hide();

        GM_log('所有测试完成!');
        GM_log('测试结果:', testResults);

        // 显示完成摘要
        showCompletionSummary();
    }

    // 显示完成摘要
    function showCompletionSummary() {
        const successCount = testResults.filter(r => r.status === 'SUCCESS').length;
        const existsCount = testResults.filter(r => r.status === 'EXISTS').length;
        const errorCount = testResults.filter(r => r.status === 'ERROR' || r.status === 'UNKNOWN').length;

        alert(`测试完成！\n\n` +
              `总测试数量: ${testResults.length}\n` +
              `可注册账户: ${successCount}\n` +
              `账户已存在: ${existsCount}\n` +
              `错误/未知: ${errorCount}\n\n` +
              `点击"导出结果"按钮保存详细报告`);
    }

    // 导出结果
    function exportResults() {
        if (testResults.length === 0) {
            alert('暂无测试结果可导出');
            return;
        }

        let csvContent = 'Index,FirstName,LastName,BirthDate,SSN,Status,Message,Timestamp\n';

        testResults.forEach(result => {
            csvContent += `${result.index},"${result.firstName}","${result.lastName}","${result.birthDate}","${result.ssn}","${result.status}","${result.message}","${result.timestamp}"\n`;
        });

        // 创建下载链接
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `FSA_Test_Results_${new Date().toISOString().slice(0,10)}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        GM_log('结果已导出为CSV文件');
    }

    // 查找带有特定文本的按钮
    function findButtonByText(text) {
        // 使用更精确的选择器，基于ID和文本内容
        const buttons = Array.from(document.querySelectorAll('button'));
        return buttons.find(btn => btn.textContent.includes(text));
    }

    // 加载之前的数据和结果
    function loadPreviousData() {
        // 加载导入的数据
        const savedData = GM_getValue('fsa_imported_data', null);
        if (savedData) {
            try {
                testData = JSON.parse(savedData);
                updateDataCount();
                updateProgressDisplay();

                // 如果有数据，启用开始测试按钮
                if (testData.length > 0) {
                    $('#start-test').prop('disabled', false);
                    $('#test-status').text('待开始');
                }

                GM_log('已加载之前导入的数据:', testData.length, '条');
            } catch (error) {
                GM_log('加载导入数据时出错:', error);
            }
        }

        // 加载测试结果
        const savedResults = GM_getValue('fsa_test_results', null);
        if (savedResults) {
            try {
                testResults = JSON.parse(savedResults);
                updateResultsDisplay();
                GM_log('已加载之前的测试结果:', testResults.length, '条');
            } catch (error) {
                GM_log('加载测试结果时出错:', error);
            }
        }
    }

    // 自动处理会话超时对话框
    function setupSessionKeepAlive() {
        // 监听会话超时对话框
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        // 检查是否是会话超时对话框
                        const sessionDialog = node.querySelector ?
                            node.querySelector('[role="dialog"]') ||
                            (node.getAttribute && node.getAttribute('role') === 'dialog' ? node : null) :
                            null;

                        if (sessionDialog) {
                            const dialogText = sessionDialog.textContent || '';
                            if (dialogText.includes('Are You Still There') ||
                                dialogText.includes('session will time out') ||
                                dialogText.includes('Still Here')) {

                                GM_log('🔄 检测到会话超时对话框，自动保持会话...');

                                // 查找"I'm Still Here"按钮
                                const stillHereBtn = sessionDialog.querySelector('button') ||
                                    Array.from(sessionDialog.querySelectorAll('button')).find(btn =>
                                        btn.textContent.includes('Still Here') ||
                                        btn.textContent.includes('I\'m Still Here')
                                    );

                                if (stillHereBtn) {
                                    setTimeout(() => {
                                        stillHereBtn.click();
                                        GM_log('✅ 已自动点击"I\'m Still Here"保持会话');
                                    }, 1000);
                                }
                            }
                        }
                    }
                });
            });
        });

        // 开始监听DOM变化
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        GM_log('🛡️ 会话保持监听器已启动');
    }

    // 初始化
    function init() {
        // 延迟创建面板，确保页面加载完成
        setTimeout(() => {
            // 设置会话保持
            setupSessionKeepAlive();

            createControlPanel();
            loadPreviousData();

            // 添加美化样式和动画
            $('head').append(`
                <style>
                    /* 面板动画 */
                    @keyframes slideDown {
                        from {
                            opacity: 0;
                            transform: translateY(-20px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    @keyframes pulse {
                        0%, 100% {
                            opacity: 1;
                        }
                        50% {
                            opacity: 0.5;
                        }
                    }

                    @keyframes fadeInUp {
                        from {
                            opacity: 0;
                            transform: translateY(10px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }

                    @keyframes slideUp {
                        from {
                            opacity: 0;
                            transform: translateY(30px) scale(0.9);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0) scale(1);
                        }
                    }

                    /* 面板样式 */
                    #fsa-tester-panel {
                        animation: slideDown 0.5s ease-out;
                    }

                    /* 按钮样式 */
                    #fsa-tester-panel button:not(:disabled) {
                        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                    }

                    #fsa-tester-panel button:disabled {
                        opacity: 0.4;
                        cursor: not-allowed;
                        transform: none !important;
                        filter: grayscale(0.5);
                        background: #d1d5db !important;
                        color: #9ca3af !important;
                    }

                    /* 确认对话框按钮悬停效果 */
                    .fsa-confirm-dialog .cancel-btn:hover {
                        background: #e5e7eb;
                    }

                    .fsa-confirm-dialog .confirm-btn:hover {
                        background: #dc2626;
                        transform: translateY(-1px);
                    }

                    /* 输入框样式 */
                    #data-input:focus {
                        outline: none;
                        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    }

                    /* 滚动条样式 */
                    #results-list::-webkit-scrollbar {
                        width: 4px;
                    }

                    #results-list::-webkit-scrollbar-track {
                        background: transparent;
                    }

                    #results-list::-webkit-scrollbar-thumb {
                        background: rgba(0,0,0,0.2);
                        border-radius: 2px;
                    }

                    #results-list::-webkit-scrollbar-thumb:hover {
                        background: rgba(0,0,0,0.3);
                    }

                    /* 通知动画 */
                    .fsa-notification {
                        animation: slideDown 0.3s ease-out;
                    }

                    /* 状态指示器 */
                    .status-indicator {
                        display: inline-block;
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        margin-right: 6px;
                    }

                    .status-success {
                        background: #10b981;
                        animation: pulse 2s infinite;
                    }

                    .status-error {
                        background: #ef4444;
                    }

                    .status-warning {
                        background: #f59e0b;
                    }

                    .status-info {
                        background: #3b82f6;
                    }

                    /* 渐变文字效果 */
                    .gradient-text {
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        background-clip: text;
                    }

                    /* 卡片悬停效果 */
                    .hover-card {
                        transition: all 0.2s ease;
                    }

                    .hover-card:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                    }

                    /* 加载动画 */
                    @keyframes spin {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                    }

                    .loading-spinner {
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        border: 2px solid #f3f3f3;
                        border-top: 2px solid #3498db;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin-right: 6px;
                    }
                </style>
            `);
        }, 1500);
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();