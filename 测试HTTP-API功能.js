// FSA HTTP API功能测试脚本
// 这个脚本可以在浏览器控制台中运行，测试HTTP API直接提交功能

console.log('🚀 开始测试FSA HTTP API功能...');

// 测试数据
const testData = {
    firstName: 'JOHN',
    lastName: 'TESTAPI',
    birthDate: {
        month: '01',
        day: '15',
        year: '1990'
    },
    ssn: '123456789'
};

// 获取页面表单数据的函数
async function getPageFormData() {
    try {
        const pageData = {
            csrfToken: null,
            viewState: null,
            viewStateGenerator: null
        };
        
        // 获取CSRF token
        const csrfTokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
        if (csrfTokenElement) {
            pageData.csrfToken = csrfTokenElement.value;
            console.log('✅ 找到CSRF Token:', pageData.csrfToken.substring(0, 20) + '...');
        } else {
            console.log('⚠️ 未找到CSRF Token');
        }
        
        // 获取ViewState (ASP.NET)
        const viewStateElement = document.querySelector('input[name="__VIEWSTATE"]');
        if (viewStateElement) {
            pageData.viewState = viewStateElement.value;
            console.log('✅ 找到ViewState:', pageData.viewState.substring(0, 20) + '...');
        } else {
            console.log('⚠️ 未找到ViewState');
        }
        
        // 获取ViewStateGenerator
        const viewStateGenElement = document.querySelector('input[name="__VIEWSTATEGENERATOR"]');
        if (viewStateGenElement) {
            pageData.viewStateGenerator = viewStateGenElement.value;
            console.log('✅ 找到ViewStateGenerator:', pageData.viewStateGenerator);
        } else {
            console.log('⚠️ 未找到ViewStateGenerator');
        }
        
        return pageData;
        
    } catch (error) {
        console.error('❌ 获取页面表单数据失败:', error);
        return {
            csrfToken: null,
            viewState: null,
            viewStateGenerator: null
        };
    }
}

// 分析注册API响应的函数
function analyzeRegistrationResponse(apiResponse) {
    try {
        console.log('📊 分析API响应:', apiResponse);

        // 检查API响应的状态
        if (apiResponse.error || apiResponse.errors) {
            const errorMessage = apiResponse.error ||
                               (apiResponse.errors && apiResponse.errors.length > 0 ?
                                apiResponse.errors[0].message : '未知错误');

            console.log('❌ API返回错误:', errorMessage);

            // 检查是否是账户已存在的错误
            if (errorMessage.includes('already exists') ||
                errorMessage.includes('Account already exists') ||
                errorMessage.includes('SSN') ||
                apiResponse.accountExists === true) {
                return {
                    status: "EXISTS",
                    message: "账户已存在"
                };
            }

            return {
                status: "ERROR",
                message: `API错误: ${errorMessage}`
            };
        }

        // 检查成功响应
        if (apiResponse.success === true ||
            apiResponse.status === 'success' ||
            apiResponse.canProceed === true) {
            console.log('✅ API返回成功状态');
            return {
                status: "SUCCESS",
                message: "可以创建账户"
            };
        }

        // 检查是否需要进入下一步
        if (apiResponse.nextStep || apiResponse.redirect) {
            console.log('➡️ API指示进入下一步');
            return {
                status: "SUCCESS",
                message: "验证通过，可以继续"
            };
        }

        // 默认情况 - 分析响应内容
        const responseStr = JSON.stringify(apiResponse);
        console.log('🔍 分析响应字符串:', responseStr.substring(0, 200));

        if (responseStr.includes('exists') || responseStr.includes('duplicate')) {
            return {
                status: "EXISTS",
                message: "账户可能已存在"
            };
        }

        return {
            status: "UNKNOWN",
            message: `未知API响应: ${responseStr.substring(0, 100)}`
        };

    } catch (error) {
        console.error('❌ 分析API响应失败:', error);
        return {
            status: "ERROR",
            message: `响应分析错误: ${error.message}`
        };
    }
}

// 分析HTTP响应的函数（保留用于兼容）
function analyzeHttpResponse(responseText) {
    try {
        console.log('📄 响应长度:', responseText.length, '字符');

        // 检查响应中的关键信息
        if (responseText.includes("Account already exists") ||
            responseText.includes("账户已存在") ||
            responseText.includes("already has an FSA ID")) {
            return {
                status: "EXISTS",
                message: "账户已存在"
            };
        }

        // 检查是否成功进入下一步
        if (responseText.includes("account-info") ||
            responseText.includes("Account Information") ||
            responseText.includes("Continue")) {
            return {
                status: "SUCCESS",
                message: "可以创建账户"
            };
        }

        // 检查是否有验证错误
        if (responseText.includes("validation-summary") ||
            responseText.includes("field-validation-error") ||
            responseText.includes("error") ||
            responseText.includes("invalid")) {
            return {
                status: "ERROR",
                message: "表单验证错误"
            };
        }

        // 检查是否被重定向到错误页面
        if (responseText.includes("error") || responseText.includes("Error")) {
            return {
                status: "ERROR",
                message: "服务器错误"
            };
        }

        // 默认情况
        return {
            status: "UNKNOWN",
            message: "未知响应状态"
        };

    } catch (error) {
        console.error('❌ 分析HTTP响应失败:', error);
        return {
            status: "ERROR",
            message: `响应分析错误: ${error.message}`
        };
    }
}

// 主测试函数 - 使用真实的FSA API
async function testHttpAPI() {
    try {
        console.log('🔍 步骤1: 检查生日API...');
        const dobResult = await testCheckDateOfBirth();

        if (!dobResult.success) {
            console.log('⚠️ 生日检查失败，继续测试注册API...');
        }

        console.log('🔧 步骤2: 测试注册API...');
        const registrationResult = await testRegistrationAPI();

        console.log('🎯 步骤3: 分析最终结果...');
        console.log('注册结果:', registrationResult);

        return registrationResult;

    } catch (error) {
        console.error('❌ FSA API测试失败:', error);
        return {
            status: "ERROR",
            message: `测试错误: ${error.message}`
        };
    }
}

// 获取XSRF Token
function getXSRFToken() {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'XSRF-TOKEN') {
            return decodeURIComponent(value);
        }
    }
    return null;
}

// 测试生日检查API
async function testCheckDateOfBirth() {
    try {
        const dobData = {
            month: parseInt(testData.birthDate.month),
            day: parseInt(testData.birthDate.day),
            year: parseInt(testData.birthDate.year)
        };

        // 获取XSRF token
        const xsrfToken = getXSRFToken();
        if (!xsrfToken) {
            throw new Error('无法获取XSRF token');
        }

        console.log('📤 发送生日检查请求:', dobData);
        console.log('🔑 XSRF Token:', xsrfToken.substring(0, 8) + '...');

        const response = await fetch('/app/api/auth/registration/checkDob', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-XSRF-TOKEN': xsrfToken
            },
            credentials: 'same-origin',
            body: JSON.stringify(dobData)
        });

        console.log('📥 生日检查响应状态:', response.status, response.statusText);

        if (!response.ok) {
            console.log('❌ 生日检查API错误:', response.status);
            return { success: false, message: `API错误: ${response.status}` };
        }

        const result = await response.json();
        console.log('✅ 生日检查结果:', result);

        return { success: true, data: result };

    } catch (error) {
        console.error('❌ 生日检查失败:', error);
        return { success: false, message: error.message };
    }
}

// 测试注册API
async function testRegistrationAPI() {
    try {
        const registrationData = {
            firstName: testData.firstName,
            middleName: '',
            lastName: testData.lastName,
            dateOfBirth: {
                month: parseInt(testData.birthDate.month),
                day: parseInt(testData.birthDate.day),
                year: parseInt(testData.birthDate.year)
            },
            ssn: testData.ssn
        };

        // 获取XSRF token
        const xsrfToken = getXSRFToken();
        if (!xsrfToken) {
            throw new Error('无法获取XSRF token');
        }

        console.log('📤 发送注册请求:', registrationData);
        console.log('🔑 XSRF Token:', xsrfToken.substring(0, 8) + '...');

        const response = await fetch('/app/api/auth/registration/a', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-XSRF-TOKEN': xsrfToken
            },
            credentials: 'same-origin',
            body: JSON.stringify(registrationData)
        });

        console.log('📥 注册API响应状态:', response.status, response.statusText);
        console.log('📥 响应头:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            console.log('❌ 注册API错误:', response.status);
            return {
                status: "ERROR",
                message: `注册API错误: ${response.status}`
            };
        }

        const result = await response.json();
        console.log('✅ 注册API响应:', result);

        // 分析API响应
        return analyzeRegistrationResponse(result);

    } catch (error) {
        console.error('❌ 注册API调用失败:', error);
        return {
            status: "ERROR",
            message: `注册API错误: ${error.message}`
        };
    }
}

// 运行测试
console.log('🎬 开始执行HTTP API测试...');
testHttpAPI().then(result => {
    console.log('🏁 测试完成!');
    console.log('最终结果:', result);
}).catch(error => {
    console.error('💥 测试异常:', error);
});

// 导出函数供外部使用
window.testFSAHttpAPI = testHttpAPI;
window.testCheckDateOfBirth = testCheckDateOfBirth;
window.testRegistrationAPI = testRegistrationAPI;
window.analyzeRegistrationResponse = analyzeRegistrationResponse;
window.getFSAPageFormData = getPageFormData;
window.analyzeFSAResponse = analyzeHttpResponse;

console.log('💡 提示: 你可以在控制台中调用以下函数:');
console.log('- testFSAHttpAPI(): 运行完整的FSA API测试');
console.log('- testCheckDateOfBirth(): 测试生日检查API');
console.log('- testRegistrationAPI(): 测试注册API');
console.log('- analyzeRegistrationResponse(apiResponse): 分析API响应');
console.log('- getFSAPageFormData(): 获取页面表单数据（兼容）');
console.log('- analyzeFSAResponse(responseText): 分析HTML响应（兼容）');
