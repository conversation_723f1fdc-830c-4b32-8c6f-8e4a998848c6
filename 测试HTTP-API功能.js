// FSA HTTP API功能测试脚本
// 这个脚本可以在浏览器控制台中运行，测试HTTP API直接提交功能

console.log('🚀 开始测试FSA HTTP API功能...');

// 测试数据
const testData = {
    firstName: 'JOHN',
    lastName: 'TESTAPI',
    birthDate: {
        month: '01',
        day: '15',
        year: '1990'
    },
    ssn: '123456789'
};

// 获取页面表单数据的函数
async function getPageFormData() {
    try {
        const pageData = {
            csrfToken: null,
            viewState: null,
            viewStateGenerator: null
        };
        
        // 获取CSRF token
        const csrfTokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
        if (csrfTokenElement) {
            pageData.csrfToken = csrfTokenElement.value;
            console.log('✅ 找到CSRF Token:', pageData.csrfToken.substring(0, 20) + '...');
        } else {
            console.log('⚠️ 未找到CSRF Token');
        }
        
        // 获取ViewState (ASP.NET)
        const viewStateElement = document.querySelector('input[name="__VIEWSTATE"]');
        if (viewStateElement) {
            pageData.viewState = viewStateElement.value;
            console.log('✅ 找到ViewState:', pageData.viewState.substring(0, 20) + '...');
        } else {
            console.log('⚠️ 未找到ViewState');
        }
        
        // 获取ViewStateGenerator
        const viewStateGenElement = document.querySelector('input[name="__VIEWSTATEGENERATOR"]');
        if (viewStateGenElement) {
            pageData.viewStateGenerator = viewStateGenElement.value;
            console.log('✅ 找到ViewStateGenerator:', pageData.viewStateGenerator);
        } else {
            console.log('⚠️ 未找到ViewStateGenerator');
        }
        
        return pageData;
        
    } catch (error) {
        console.error('❌ 获取页面表单数据失败:', error);
        return {
            csrfToken: null,
            viewState: null,
            viewStateGenerator: null
        };
    }
}

// 分析HTTP响应的函数
function analyzeHttpResponse(responseText) {
    try {
        console.log('📄 响应长度:', responseText.length, '字符');
        
        // 检查响应中的关键信息
        if (responseText.includes("Account already exists") || 
            responseText.includes("账户已存在") ||
            responseText.includes("already has an FSA ID")) {
            return {
                status: "EXISTS",
                message: "账户已存在"
            };
        }
        
        // 检查是否成功进入下一步
        if (responseText.includes("account-info") || 
            responseText.includes("Account Information") ||
            responseText.includes("Continue")) {
            return {
                status: "SUCCESS", 
                message: "可以创建账户"
            };
        }
        
        // 检查是否有验证错误
        if (responseText.includes("validation-summary") ||
            responseText.includes("field-validation-error") ||
            responseText.includes("error") ||
            responseText.includes("invalid")) {
            return {
                status: "ERROR",
                message: "表单验证错误"
            };
        }
        
        // 检查是否被重定向到错误页面
        if (responseText.includes("error") || responseText.includes("Error")) {
            return {
                status: "ERROR",
                message: "服务器错误"
            };
        }
        
        // 默认情况
        return {
            status: "UNKNOWN",
            message: "未知响应状态"
        };
        
    } catch (error) {
        console.error('❌ 分析HTTP响应失败:', error);
        return {
            status: "ERROR",
            message: `响应分析错误: ${error.message}`
        };
    }
}

// 主测试函数
async function testHttpAPI() {
    try {
        console.log('🔍 步骤1: 获取页面表单数据...');
        const pageData = await getPageFormData();
        
        console.log('🔧 步骤2: 构建表单数据...');
        const formData = new URLSearchParams();
        formData.append('fsa_Input_FirstName', testData.firstName);
        formData.append('fsa_Input_LastName', testData.lastName);
        formData.append('fsa_Input_DateOfBirthMonth', testData.birthDate.month);
        formData.append('fsa_Input_DateOfBirthDay', testData.birthDate.day);
        formData.append('fsa_Input_DateOfBirthYear', testData.birthDate.year);
        formData.append('fsa_Input_Ssn', testData.ssn);
        
        // 添加页面的隐藏字段
        if (pageData.csrfToken) {
            formData.append('__RequestVerificationToken', pageData.csrfToken);
        }
        if (pageData.viewState) {
            formData.append('__VIEWSTATE', pageData.viewState);
        }
        if (pageData.viewStateGenerator) {
            formData.append('__VIEWSTATEGENERATOR', pageData.viewStateGenerator);
        }
        
        // 添加提交按钮
        formData.append('fsa_Button_PersonalInformationContinue', 'Continue');
        
        console.log('📤 步骤3: 发送HTTP请求...');
        console.log('URL:', window.location.href);
        console.log('表单数据:', Object.fromEntries(formData.entries()));
        
        const response = await fetch(window.location.href, {
            method: 'POST',
            body: formData,
            credentials: 'same-origin',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Referer': window.location.href,
                'User-Agent': navigator.userAgent
            }
        });
        
        console.log('📥 步骤4: 处理响应...');
        console.log('响应状态:', response.status, response.statusText);
        console.log('响应头:', Object.fromEntries(response.headers.entries()));
        
        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
        }
        
        const responseText = await response.text();
        const result = analyzeHttpResponse(responseText);
        
        console.log('🎯 步骤5: 分析结果...');
        console.log('结果状态:', result.status);
        console.log('结果消息:', result.message);
        
        // 显示响应的前500个字符用于调试
        console.log('📄 响应内容预览:');
        console.log(responseText.substring(0, 500) + '...');
        
        return result;
        
    } catch (error) {
        console.error('❌ HTTP API测试失败:', error);
        return {
            status: "ERROR",
            message: `测试错误: ${error.message}`
        };
    }
}

// 运行测试
console.log('🎬 开始执行HTTP API测试...');
testHttpAPI().then(result => {
    console.log('🏁 测试完成!');
    console.log('最终结果:', result);
}).catch(error => {
    console.error('💥 测试异常:', error);
});

// 导出函数供外部使用
window.testFSAHttpAPI = testHttpAPI;
window.getFSAPageFormData = getPageFormData;
window.analyzeFSAResponse = analyzeHttpResponse;

console.log('💡 提示: 你可以在控制台中调用以下函数:');
console.log('- testFSAHttpAPI(): 运行完整测试');
console.log('- getFSAPageFormData(): 获取页面表单数据');
console.log('- analyzeFSAResponse(responseText): 分析响应内容');
