# FSA账户测试器使用说明

## 功能介绍
FSA账户测试器是一个自动化工具，用于批量测试FSA账户的注册状态。支持多种数据格式的直接粘贴导入。

## 安装方法
1. 安装Tampermonkey浏览器扩展
2. 将`fsa-account-tester.user.js`脚本导入Tampermonkey
3. 访问FSA注册页面：https://studentaid.gov/fsa-id/create-account/

## 支持的数据格式

### 1. 空格分隔格式（推荐）
```
SAMANTHA STONE 07/21/1998 *********
JOHN DOE 12/15/1995 *********
MARY JOHNSON 03/08/2000 *********
```

### 2. 逗号分隔格式
```
SAMANTHA,STONE,07/21/1998,*********
JOHN,DOE,12/15/1995,*********  
MARY,JOHNSON,03/08/2000,*********
```

### 3. TAB分隔格式
```
SAMANTHA	STONE	07/21/1998	*********
JOHN	DOE	12/15/1995	*********
MARY	JOHNSON	03/08/2000	*********
```

### 支持的日期格式
- MM/DD/YYYY: 07/21/1998
- DD/MM/YYYY: 21/07/1998  
- YYYY/MM/DD: 1998/07/21
- MM-DD-YYYY: 07-21-1998
- DD-MM-YYYY: 21-07-1998
- YYYY-MM-DD: 1998-07-21
- MMDDYYYY: ********
- YYYYMMDD: ********
- YYYYMM00: ******** (日期00自动调整为01)

## 使用步骤

### 1. 准备数据
- 每行一条记录
- 每条记录包含4个字段：姓名、姓氏、出生日期、SSN
- 可以从Excel、CSV文件或其他源直接复制粘贴

### 2. 导入数据
1. 打开FSA注册页面
2. 在右上角找到"FSA账户测试器"面板
3. 将准备好的数据粘贴到文本框中
4. 点击"导入数据"按钮
5. 确认数据导入成功

### 3. 开始测试
1. 点击"开始测试"按钮
2. 脚本会自动：
   - 填写个人信息表单
   - 提交表单
   - 检查返回结果
   - 记录测试状态
   - 自动进行下一条测试

### 4. 查看结果
- 实时查看测试进度和结果
- 支持的状态：
  - ✓ 可注册：账户可以成功创建
  - ✗ 已存在：账户已经存在
  - ? 未知：出现错误或未知状态

### 5. 导出结果
1. 测试完成后点击"导出结果"按钮
2. 下载CSV格式的详细报告
3. 报告包含所有测试记录和时间戳

## 功能特性

### 智能格式识别
- 自动识别多种分隔符格式
- 智能解析多种日期格式
- 自动调整无效日期（00→01）
- 自动清理多余的空白和引号
- 向后兼容原有TAB格式

### 智能姓氏处理
- 自动处理包含空格的姓氏
- 保留姓氏的最后部分（如：TRAN NGUYEN → NGUYEN）
- 避免触发FSA网站的确认弹窗
- 支持复合姓氏的智能处理

### 自动弹窗处理
- 自动检测并处理"Are You Sure?"确认弹窗
- 智能识别姓氏空格确认对话框
- 自动点击"Yes"按钮继续流程
- 支持多种弹窗样式和布局

### 错误处理
- 自动重试机制（最多3次）
- 详细的错误日志记录
- 友好的错误提示信息

### 数据持久化
- 自动保存导入的数据
- 保存测试结果和进度
- 支持中断后继续测试

### 用户界面
- 可最小化的控制面板
- 实时进度显示
- 当前测试信息展示
- 结果实时更新

## 注意事项

1. **数据格式**：确保每行包含完整的4个字段
2. **网络连接**：保持稳定的网络连接
3. **页面操作**：测试期间不要手动操作页面
4. **数据安全**：测试数据会临时保存在浏览器本地
5. **合规使用**：请确保符合相关法律法规要求

## 常见问题

### Q: 数据导入失败怎么办？
A: 检查数据格式是否正确，确保每行包含4个字段，可参考示例格式。

### Q: 测试中断了怎么办？
A: 脚本会自动保存进度，刷新页面后可以继续测试。

### Q: 如何清空数据重新开始？
A: 点击"清空数据"按钮可以清除所有导入的数据和测试结果。

### Q: 支持多少条数据？
A: 理论上没有限制，但建议单次测试不超过1000条以确保稳定性。

### Q: 脚本会自动处理弹窗吗？
A: 是的，脚本会自动检测并处理"Are You Sure?"等确认弹窗，无需手动干预。

### Q: 如果弹窗没有被自动处理怎么办？
A: 脚本会尝试多种方法查找确认按钮，如果仍然失败，请检查控制台日志或手动点击确认。

## 更新日志

### v2.2
- 新增智能姓氏处理功能
- 自动处理包含空格的姓氏（保留最后部分）
- 新增YYYYMM00日期格式支持
- 自动调整日期00为01
- 避免FSA确认弹窗的触发

### v2.1
- 新增自动弹窗处理功能
- 自动处理"Are You Sure?"确认对话框
- 智能识别姓氏空格确认弹窗
- 增强测试流程的自动化程度

### v2.0 (之前版本)
- 支持多种数据格式直接粘贴
- 智能日期格式识别
- 自动重试机制
- 数据持久化
- CSV结果导出

## 技术支持
如有问题请联系开发者或查看脚本源码中的注释说明。 